<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.UserCccMapper">
    
    <resultMap type="com.tocc.common.domain.vo.UserCccVO" id="UserCccResult">
        <result property="userId"       column="user_id"       />
        <result property="loginName"    column="login_name"    />
        <result property="displayName"  column="display_name"  />
        <result property="accessKey"    column="access_key"    />
        <result property="accessSecret" column="access_secret" />
    </resultMap>

    <sql id="selectUserCccVo">
        select user_id, login_name, display_name, access_key, access_secret
        from user_ccc
    </sql>

    <select id="selectByUserId" parameterType="String" resultMap="UserCccResult">
        <include refid="selectUserCccVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectNotCreated" resultMap="UserCccResult">
        SELECT * FROM user_ccc WHERE access_secret IS NULL OR access_secret=''
    </select>

    <select id="selectCreated" resultMap="UserCccResult">
        SELECT * FROM user_ccc WHERE access_secret IS NOT NULL
    </select>

    <insert id="save" parameterType="com.tocc.common.domain.vo.UserCccVO">
    INSERT INTO user_ccc
    (user_id,
    login_name,
    display_name,
    access_key,
    access_secret)
    VALUES
    (#{userId},
    #{loginName},
    #{displayName},
    #{accessKey},
    #{accessSecret})
    ON DUPLICATE KEY UPDATE
    user_id=values(user_id),
    login_name=values(login_name),
    display_name=values(display_name),
    access_key=values(access_key),
    access_secret=values(access_secret)
    </insert>

</mapper>
