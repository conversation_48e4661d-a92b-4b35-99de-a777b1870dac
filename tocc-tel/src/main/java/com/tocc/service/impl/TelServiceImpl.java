package com.tocc.service.impl;

import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.tocc.common.domain.vo.UserCccVO;
import com.tocc.common.utils.AliyunCccUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.mapper.UserCccMapper;
import com.tocc.service.ITelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("telService")
public class TelServiceImpl implements ITelService {

    private static final Logger log = LoggerFactory.getLogger(TelServiceImpl.class);

    @Autowired
    private UserCccMapper userCccMapper;

    @Override
    public String api(String userId, String action, String request) {
        //获取用户AK、AS信息
        UserCccVO vo = userCccMapper.selectByUserId(userId);
        if(vo == null) {
            throw new IllegalArgumentException("无云呼坐席，请联系管理员创建坐席");
        }

        //接口名称、参数校验
        if(StringUtils.isBlank(action)) {
            throw new IllegalArgumentException("action接口名称不能为空");
        }
        if(StringUtils.isBlank(request)) {
            throw new IllegalArgumentException("request参数不能为空");
        }

        //判断权限，是否有云呼密钥
        if(StringUtils.isBlank(vo.getAccessKey()) || StringUtils.isBlank(vo.getAccessSecret())) {
            throw new IllegalArgumentException("无云呼坐席，请联系管理员创建坐席密钥");
        }

        vo.setAction(action);
        vo.setRequest(request);
        String res = AliyunCccUtils.invokeApiByAk(vo, action, request);
        return res;
    }

    public boolean createUser() {
        //userCcc获取没有创建坐席的用户
        List<UserCccVO> list = userCccMapper.selectNotCreated();
        for(UserCccVO vo : list) {
            try {
                vo = AliyunCccUtils.createUser(vo);
            } catch (ServerException e) {
                e.printStackTrace();
            } catch (ClientException e) {
                e.printStackTrace();
            }
            //保存AK、AS数据库
            userCccMapper.save(vo);
        }
        return true;
    }

}
