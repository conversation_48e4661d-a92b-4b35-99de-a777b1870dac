package com.tocc.service.impl;

import com.tocc.common.domain.dto.SmsTemplateDTO;
import com.tocc.common.utils.AliyunSmsUtils;
import com.tocc.service.IIndexService;
import org.apache.commons.lang3.StringUtils;

public class IndexServiceImpl implements IIndexService {

    public boolean smsSend(SmsTemplateDTO dto) {
        dto.setTemplateId("SMS_487315645");
//		AliyunSmsUtils.ALIYUN_SIGN_NAME = "广西交科集团";
        if (StringUtils.isNoneBlank(dto.getSignName())) {
            AliyunSmsUtils.ALIYUN_SIGN_NAME = dto.getSignName();
        }
        String templateKey = dto.getTemplateKey();
        if ("UPDATE_EMER_EVENT".equals(templateKey)) {
            dto.setTemplateId("SMS_488145011");
        } else if ("CONFIRM_EMER_EVENT".equals(templateKey)) {
            dto.setTemplateId("SMS_488890013");
        }

        return AliyunSmsUtils.sendAnSms(dto);
    }

}
