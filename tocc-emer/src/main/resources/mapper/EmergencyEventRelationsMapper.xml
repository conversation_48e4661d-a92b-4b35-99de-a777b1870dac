<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.emergency.mapper.EmergencyEventRelationsMapper">
    
    <resultMap type="EmergencyEventRelations" id="EmergencyEventRelationsResult">
        <result property="eventRelationsId"    column="event_relations_id"    />
        <result property="eventId"    column="event_id"    />
        <result property="prePlanId"    column="pre_plan_id"    />
        <result property="expertInfoIds"    column="expert_info_ids"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="rescueTeamId"    column="rescue_team_id"    />
        <result property="healthUnitId"    column="health_unit_id"    />
        <result property="fireUnitId"    column="fire_unit_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmergencyEventRelationsVo">
        select event_relations_id, event_id, pre_plan_id, expert_info_ids, warehouse_id, rescue_team_id, health_unit_id, fire_unit_id, create_time, creator, update_time, updater, del_flag from emergency_event_relations
    </sql>

    <select id="selectEmergencyEventRelationsList" parameterType="EmergencyEventRelations" resultMap="EmergencyEventRelationsResult">
        <include refid="selectEmergencyEventRelationsVo"/>
        <where>
            and del_flag = '0'
            <if test="eventId != null  and eventId != ''"> and event_id = #{eventId}</if>
            <if test="prePlanId != null  and prePlanId != ''"> and pre_plan_id = #{prePlanId}</if>
            <if test="expertInfoIds != null  and expertInfoIds != ''"> and expert_info_ids = #{expertInfoIds}</if>
            <if test="warehouseId != null  and warehouseId != ''"> and warehouse_id = #{warehouseId}</if>
            <if test="rescueTeamId != null  and rescueTeamId != ''"> and rescue_team_id = #{rescueTeamId}</if>
            <if test="healthUnitId != null  and healthUnitId != ''"> and health_unit_id = #{healthUnitId}</if>
            <if test="fireUnitId != null  and fireUnitId != ''"> and fire_unit_id = #{fireUnitId}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>
    
    <select id="selectEmergencyEventRelationsByEventRelationsId" parameterType="String" resultMap="EmergencyEventRelationsResult">
        <include refid="selectEmergencyEventRelationsVo"/>
        where event_relations_id = #{eventRelationsId} and  del_flag = '0'
    </select>



    <insert id="insertEmergencyEventRelations" parameterType="EmergencyEventRelations">
        insert into emergency_event_relations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventRelationsId != null">event_relations_id,</if>
            <if test="eventId != null">event_id,</if>
            <if test="prePlanId != null">pre_plan_id,</if>
            <if test="expertInfoIds != null">expert_info_ids,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="rescueTeamId != null">rescue_team_id,</if>
            <if test="healthUnitId != null">health_unit_id,</if>
            <if test="fireUnitId != null">fire_unit_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventRelationsId != null">#{eventRelationsId},</if>
            <if test="eventId != null">#{eventId},</if>
            <if test="prePlanId != null">#{prePlanId},</if>
            <if test="expertInfoIds != null">#{expertInfoIds},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="rescueTeamId != null">#{rescueTeamId},</if>
            <if test="healthUnitId != null">#{healthUnitId},</if>
            <if test="fireUnitId != null">#{fireUnitId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmergencyEventRelations" parameterType="EmergencyEventRelations">
        update emergency_event_relations
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="prePlanId != null">pre_plan_id = #{prePlanId},</if>
            <if test="expertInfoIds != null">expert_info_ids = #{expertInfoIds},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="rescueTeamId != null">rescue_team_id = #{rescueTeamId},</if>
            <if test="healthUnitId != null">health_unit_id = #{healthUnitId},</if>
            <if test="fireUnitId != null">fire_unit_id = #{fireUnitId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where event_relations_id = #{eventRelationsId}
    </update>

    <update id="deleteEmergencyEventRelationsByEventRelationsId" parameterType="String">
        update  emergency_event_relations  set   del_flag = 1 where event_relations_id = #{eventRelationsId}
    </update>

    <update id="deleteEmergencyEventRelationsByEventRelationsIds">
        update emergency_event_relations set del_flag = 1 where event_relations_id in
        <foreach item="eventRelationsId" collection="eventRelationsIds" open="(" separator="," close=")">
            #{eventRelationsId}
        </foreach>
    </update>
</mapper>