package com.tocc.domain.vo;

import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.system.domain.vo.ExpertInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 应急事件关联对象 emergency_event_relations
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
public class EmergencyEventRelationsVo extends BaseEntity
{

    /** 主键 */
    @ApiModelProperty(value = "关联ID ")
    private String eventRelationsId;


    /** 应急事件 */
    @ApiModelProperty(value = "应急事件 ")
    private EmergencyEventVO emergencyEventVO;

    /** 应急预案 */
    @ApiModelProperty(value = "应急预案 ")
    private EmPrePlanVO emPrePlanVO;

    /** 推荐专家 */
    @ApiModelProperty(value = "推荐专家 ")
    private List<ExpertInfoVO> expertInfoVOList;

    /** 应急物资 */
    private WarehouseCircleVO warehouseCircleVO;

    /** 救援队伍 */

    private RescueTeamCircleVO rescueTeamCircleVO;

    /** 医疗单位ID(预留) */

    private String healthUnitId;

    /** 消防单位ID（预留) */

    private String fireUnitId;

    /** 创建人 */

    private String creator;

    /** 更新人 */

    private String updater;

    /** 删除标志(0存在/1删除) */
    private Integer delFlag;


}
