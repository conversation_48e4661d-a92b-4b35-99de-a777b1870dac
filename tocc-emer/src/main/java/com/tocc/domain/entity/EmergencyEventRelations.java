package com.tocc.domain.entity;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 应急事件关联对象 emergency_event_relations
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
public class EmergencyEventRelations extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String eventRelationsId;

    /** 应急事件ID */
    @Excel(name = "应急事件ID")
    private String eventId;

    /** 应急预案ID */
    @Excel(name = "应急预案ID")
    private String prePlanId;

    /** 推荐专家ID数组 */
    @Excel(name = "推荐专家ID数组")
    private String expertInfoIds;

    /** 应急物资仓库ID， */
    @Excel(name = "应急物资仓库ID，")
    private String warehouseId;

    /** 救援队伍ID */
    @Excel(name = "救援队伍ID")
    private String rescueTeamId;

    /** 医疗单位ID(预留) */
    @Excel(name = "医疗单位ID(预留)")
    private String healthUnitId;

    /** 消防单位ID（预留) */
    @Excel(name = "消防单位ID", readConverterExp = "消防单位ID（预留)")
    private String fireUnitId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    /** 删除标志(0存在/1删除) */
    private Integer delFlag;


}
