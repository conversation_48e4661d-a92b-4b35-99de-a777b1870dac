package com.tocc.service;

import java.util.List;
import com.tocc.domain.entity.EmergencyEventRelations;
import com.tocc.domain.vo.EmergencyEventRelationsVo;

/**
 * 应急事件关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IEmergencyEventRelationsService 
{
    /**
     * 查询应急事件关联
     * 
     * @param eventRelationsId 应急事件关联主键
     * @return 应急事件关联
     */
    public EmergencyEventRelations selectEmergencyEventRelationsByEventRelationsId(String eventRelationsId);

    /**
     * 查询应急事件关联列表
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 应急事件关联集合
     */
    public List<EmergencyEventRelations> selectEmergencyEventRelationsList(EmergencyEventRelations emergencyEventRelations);

    /**
     * 新增应急事件关联
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 结果
     */
    public int insertEmergencyEventRelations(EmergencyEventRelations emergencyEventRelations);

    /**
     * 修改应急事件关联
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 结果
     */
    public int updateEmergencyEventRelations(EmergencyEventRelations emergencyEventRelations);

    /**
     * 批量删除应急事件关联
     * 
     * @param eventRelationsIds 需要删除的应急事件关联主键集合
     * @return 结果
     */
    public int deleteEmergencyEventRelationsByEventRelationsIds(String[] eventRelationsIds);

    /**
     * 删除应急事件关联信息
     * 
     * @param eventRelationsId 应急事件关联主键
     * @return 结果
     */
    public int deleteEmergencyEventRelationsByEventRelationsId(String eventRelationsId);

    /**
     *
     * @param eventId 应急事件ID
     * @return
     */
    public EmergencyEventRelationsVo getInfoByEventId(String eventId);
}

