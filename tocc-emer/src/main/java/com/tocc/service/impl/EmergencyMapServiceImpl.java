package com.tocc.service.impl;



import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.dto.EmergencyEventDTO;
import com.tocc.domain.dto.RescueTeamDTO;
import com.tocc.domain.dto.WarehouseDTO;
import com.tocc.domain.vo.RescueTeamVO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.risk.vo.EmergencyMapStatisticsVO;

import com.tocc.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class EmergencyMapServiceImpl implements IEmergencyMapService {

    @Resource
    private IEmergencyEventService iEmergencyEventService;

    @Resource
    private IRescueTeamService rescueTeamService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IAlarmService alarmService;

    @Override
    public EmergencyMapStatisticsVO getStatistics() {

        EmergencyMapStatisticsVO emergencyMapStatisticsVO = new EmergencyMapStatisticsVO();

        EmergencyEventDTO emergencyEventDTO = new EmergencyEventDTO();
        emergencyEventDTO.setStatus("2");
        iEmergencyEventService.countEmergencyEvent(emergencyEventDTO);

        RescueTeamDTO   rescueTeamDTO= new RescueTeamDTO();

        List<RescueTeamVO> rescueTeamVOS = rescueTeamService.selectRescueTeamList(rescueTeamDTO);


        WarehouseDTO warehouseDTO = new WarehouseDTO();
        List<WarehouseVO> warehouseVOS = warehouseService.selectWarehouseList(warehouseDTO);


        AlarmInfoDTO alarmInfoDTO = new AlarmInfoDTO();
        alarmInfoDTO.setStatus("0");


        emergencyMapStatisticsVO.setEmergencyEventQuantity( iEmergencyEventService.countEmergencyEvent(emergencyEventDTO));
        emergencyMapStatisticsVO.setMaterialQuantity(rescueTeamVOS.size());
        emergencyMapStatisticsVO.setRescueTeamQuantity(warehouseVOS.size());
        emergencyMapStatisticsVO.setAlarmInfoQuantity(alarmService.countAlarmInfo(alarmInfoDTO));

        return emergencyMapStatisticsVO;
    }
}
