package com.tocc.service;

import com.tocc.domain.dto.EmergencyEventCreateDTO;
import com.tocc.domain.dto.EmergencyEventDTO;
import com.tocc.domain.vo.EmergencyEventVO;
import com.tocc.domain.vo.EmergencyEventDetailVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 应急事件Service接口
 * 
 * <AUTHOR>
 */
public interface IEmergencyEventService {
    
    /**
     * 查询应急事件
     * 
     * @param eventId 应急事件主键
     * @return 应急事件
     */
    EmergencyEventVO selectEmergencyEventByEventId(String eventId);

    /**
     * 查询应急事件详情（包含扩展信息）
     * 
     * @param eventId 应急事件主键
     * @return 应急事件详情
     */
    EmergencyEventDetailVO selectEmergencyEventDetailByEventId(String eventId);

    /**
     * 查询应急事件列表
     *
     * @param emergencyEvent 应急事件
     * @return 应急事件集合
     */
    List<EmergencyEventVO> selectEmergencyEventList(EmergencyEventDTO emergencyEvent);

    /**
     * 新增应急事件
     * 
     * @param createDTO 应急事件创建DTO
     * @return 结果
     */
    int insertEmergencyEvent(EmergencyEventCreateDTO createDTO);

    /**
     * 修改应急事件
     * 
     * @param createDTO 应急事件修改DTO
     * @return 结果
     */
    int updateEmergencyEvent(EmergencyEventCreateDTO createDTO);

    /**
     * 批量删除应急事件
     * 
     * @param eventIds 需要删除的应急事件主键集合
     * @return 结果
     */
    int deleteEmergencyEventByEventIds(String[] eventIds);

    /**
     * 删除应急事件信息
     * 
     * @param eventId 应急事件主键
     * @return 结果
     */
    int deleteEmergencyEventByEventId(String eventId);

    /**
     * 更新事件状态
     * 
     * @param eventId 事件ID
     * @param status 新状态
     * @return 结果
     */
    int updateEventStatus(String eventId, String status);

    /**
     * 根据事件类型和时间范围查询事件列表
     * 
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 事件列表
     */
    List<EmergencyEventVO> selectEventsByTypeAndTime(String eventType, Long startTime, Long endTime);

    /**
     * 根据状态查询事件列表
     * 
     * @param status 状态
     * @return 事件列表
     */
    List<EmergencyEventVO> selectEventsByStatus(String status);

    /**
     * 统计应急事件数量
     * 
     * @param emergencyEvent 查询条件
     * @return 统计数量
     */
    int countEmergencyEvent(EmergencyEventDTO emergencyEvent);

    /**
     * 导出成立应对应急事件小组通知
     *
     * @param response
     * @param eventId    事件id
     * @param eventLevel
     */
    void downloadNotice(HttpServletResponse response, String eventId, String eventLevel);

    /**
     * 导出应急事件辅助决策word文档
     * @param eventId
     * @param response
     */
    void exportEmergencyEventDecisionDocx(String eventId, HttpServletResponse response);

    /**
     * 应急演练消息提醒
     */
    Boolean sendMsg(String content,String mobile);
}
