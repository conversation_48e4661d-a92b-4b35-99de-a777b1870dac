package com.tocc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.domain.dto.DrillReminderDto;
import com.tocc.domain.entity.DrillPlan;
import com.tocc.domain.entity.DrillReminder;
import com.tocc.domain.vo.DrillRemainderVo;
import com.tocc.mapper.DrillReminderMapper;
import com.tocc.service.IDrillPlanService;
import com.tocc.service.IDrillReminderService;
import com.tocc.service.IEmergencyEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.util.Collections;
import java.util.List;

@Service
public class DrillReminderServiceImpl extends ServiceImpl<DrillReminderMapper, DrillReminder> implements IDrillReminderService {

    @Autowired
    private IDrillPlanService drillPlanService;

    @Autowired
    private IEmergencyEventService emergencyEventService;




    @Override
    public Boolean saveEntity(DrillReminderDto dto) {


        DrillReminder drillReminder = BeanUtil.copyProperties(dto, DrillReminder.class);

        if (ObjectUtil.isNotNull(drillReminder.getId())){
            this.updateById(drillReminder);
        }else {
            drillReminder.setCreateBy(SecurityUtils.getUsername());
            this.save(drillReminder);
        }
        List<DrillPlan> drillPlans = drillPlanService.listByIds(Collections.singleton(dto.getDrillPlanId()));
        if (CollectionUtil.isNotEmpty(drillPlans)){
            DrillPlan drillPlan = drillPlans.get(0);
            if ("Y".equals(dto.getIsDrill())){
                String template = template("1");
                String format = StrUtil.format(template, DateUtil.format(DateUtil.date(),"yyyy年MM月dd日HH时mm分"), DateUtil.format(dto.getDeadline(), "yyyy年MM月dd日HH时mm分"));
//                emergencyEventService.sendMsg(format,drillPlan.getPhone());
            }

            if ("Y".equals(dto.getIsReview())){
                String template = template("2");
                String format = StrUtil.format(template, DateUtil.format(DateUtil.date(),"yyyy年MM月dd日HH时mm分"), DateUtil.format(dto.getDeadline(), "yyyy年MM月dd日HH时mm分"));
//                emergencyEventService.sendMsg(format,drillPlan.getPhone());
            }
        }
        return true;

    }

    private  String template(String type){
        String templateStr = "";
        if ("1".equals(type)){
            templateStr ="有新的资料提交预警信息待确认：时间{}，应急演练资料提交超时，请尽快提交应急演练资料，截至时间：{}。请前往系统查看。";
        }else {
            templateStr = "有新的资料提交预警信息待确认：时间{}，应急演练复盘资料提交超时，请尽快提交应急演练复盘资料，截至时间：{}。请前往系统查看。";
        }
        return templateStr;
    }

    @Override
    public DrillRemainderVo detail(Long drillPlanId) {
        List<DrillPlan> drillPlans = drillPlanService.listByIds(Collections.singleton(drillPlanId));

        if (CollectionUtil.isNotEmpty(drillPlans)){
            DrillPlan drillPlan = drillPlans.get(0);
            DrillRemainderVo drillRemainderVo = BeanUtil.copyProperties(drillPlan, DrillRemainderVo.class);
            drillRemainderVo.setDrillPlanId(drillPlan.getId());
            drillRemainderVo.setId(null);

            QueryWrapper<DrillReminder> qw = new QueryWrapper<>();
            qw.eq("drill_plan_id",drillPlanId);
            List<DrillReminder> list = this.list(qw);
            if (CollectionUtil.isNotEmpty(list)){
                drillRemainderVo.setId(list.get(0).getId());
                drillRemainderVo.setIsDrill(list.get(0).getIsDrill());
                drillRemainderVo.setIsReview(list.get(0).getIsReview());
                drillRemainderVo.setDeadline(list.get(0).getDeadline());
            }
            return drillRemainderVo;
        }
        return null;
    }
}
