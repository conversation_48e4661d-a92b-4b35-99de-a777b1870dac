package com.tocc.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.domain.entity.DrillPlan;
import com.tocc.domain.entity.DrillReviewReport;
import com.tocc.domain.dto.DrillReviewReportDto;
import com.tocc.mapper.DrillReviewReportMapper;
import com.tocc.service.IDrillPlanService;
import com.tocc.service.IDrillReviewReportService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class DrillReviewReportServiceImpl extends ServiceImpl<DrillReviewReportMapper, DrillReviewReport> implements IDrillReviewReportService {
    @Override
    public Boolean saveEntity(DrillReviewReportDto dto) {

        if (ObjectUtil.isNotNull(dto.getDrillPlanId())){
            IDrillPlanService service = SpringUtil.getBean(IDrillPlanService.class);
            List<DrillPlan> drillPlans = service.listByIds(Collections.singleton(dto.getDrillPlanId()));
            if (CollectionUtil.isNotEmpty(drillPlans)){
                DrillPlan drillPlan = drillPlans.get(0);
//                drillPlan.setReporter(dto.getReporter());
                if (drillPlan.getStatus() != 3){
                    drillPlan.setStatus(3);
                }
                service.updateById(drillPlan);
            }
        }

        DrillReviewReport drillReviewReport = BeanUtil.copyProperties(dto, DrillReviewReport.class);
        if (ObjectUtil.isNotNull(drillReviewReport.getId())){
            return this.updateById(drillReviewReport);
        }
        drillReviewReport.setCreateBy(SecurityUtils.getUsername());
        return this.save(drillReviewReport);
    }

    @Override
    public DrillReviewReport selectByDrillPlanId(Long id) {
        QueryWrapper<DrillReviewReport> qw = new QueryWrapper<>();
        qw.eq("drill_plan_id",id);
        List<DrillReviewReport> list = this.list(qw);
        if (CollectionUtil.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }
}
