package com.tocc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.domain.dto.DrillLogDto;
import com.tocc.domain.entity.DrillLog;
import com.tocc.mapper.DrillLogMapper;
import com.tocc.service.IDrillLogService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class DrillLogServiceImpl extends ServiceImpl<DrillLogMapper, DrillLog> implements IDrillLogService {
    @Override
    public Boolean saveLog(DrillLogDto dto) {
        DrillLog drillLog = BeanUtil.copyProperties(dto, DrillLog.class);
        if (ObjectUtil.isNotNull(dto.getId())){
            return this.updateById(drillLog);
        }
        return this.save(drillLog);
    }

    @Override
    public List<DrillLog> logList(Long bizId) {
        QueryWrapper<DrillLog> qw = new QueryWrapper<>();
        qw.eq("drill_desktop_id",bizId);
        return this.list(qw);
    }

}
