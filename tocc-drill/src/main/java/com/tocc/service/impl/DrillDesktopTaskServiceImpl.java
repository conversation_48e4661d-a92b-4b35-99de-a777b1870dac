package com.tocc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.domain.entity.DrillDesktopTask;
import com.tocc.mapper.DrillDesktopTaskMapper;
import com.tocc.service.IDrillDesktopTaskService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class DrillDesktopTaskServiceImpl extends ServiceImpl<DrillDesktopTaskMapper, DrillDesktopTask> implements IDrillDesktopTaskService {

    @Override
    public Boolean saveTaskList(List<DrillDesktopTask> drillDesktopTasks) {
        if (CollectionUtil.isNotEmpty(drillDesktopTasks)){
            QueryWrapper<DrillDesktopTask> qw = new QueryWrapper<>();
            qw.eq("drill_desktop_id",drillDesktopTasks.get(0).getDrillDesktopId());
            this.remove(qw);

            this.saveBatch(drillDesktopTasks);
        }

        return true;
    }

    @Override
    public List<DrillDesktopTask> listByBizId(Long bizId) {
        QueryWrapper<DrillDesktopTask> qw = new QueryWrapper<>();
        qw.eq("drill_desktop_id",bizId);

        return this.list(qw);
    }

    @Override
    public Boolean updateStatusById(Long id) {
        List<DrillDesktopTask> drillDesktopTasks = this.listByIds(Collections.singleton(id));
        if (CollectionUtil.isNotEmpty(drillDesktopTasks)){
            drillDesktopTasks.forEach(item ->{
                item.setStatus("2");
            });
            this.updateBatchById(drillDesktopTasks);
        }

        return true;
    }
}
