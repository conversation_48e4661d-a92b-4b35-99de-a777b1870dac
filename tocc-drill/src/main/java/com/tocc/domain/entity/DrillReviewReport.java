package com.tocc.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("drill_review_report")
public class DrillReviewReport  {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    @ApiModelProperty(value = "演练情况分析")
    private String analyse;

    @ApiModelProperty(value = "演练目标的实现")
    private String goalAchieved;

    @ApiModelProperty(value = "改进建议和意见")
    private String suggest;

    @ApiModelProperty(value = "评估.1,2,3,4 优、良、中、差")
    private String evaluation;

    @ApiModelProperty(value = "复盘会议室")
    private String reviewAddress;

    @ApiModelProperty(value = "复盘签到表")
    private String reviewSignIn;

    @ApiModelProperty(value = "复盘会议照片")
    private String reviewPhoto;

    @ApiModelProperty(value = "复盘报告")
    private String reviewReport;

    @ApiModelProperty(value = "关联演练计划id")
    private Long drillPlanId;

    @ApiModelProperty(value = "填报人")
    private String reporter;

    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "删除状态")
    @TableLogic(value = "0",delval = "1")
    private Integer delFlag;

    /** 创建者 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新者 */
    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
