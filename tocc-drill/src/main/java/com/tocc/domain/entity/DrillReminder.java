package com.tocc.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("drill_reminder")
public class DrillReminder {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("提醒上交演练资料 N/Y")
    private String isDrill;

    @ApiModelProperty("提醒上交复盘报告 N/Y")
    private String isReview;

    @ApiModelProperty(value = "关联演练计划id")
    private Long drillPlanId;

    @ApiModelProperty(value = "截止时间")
    private Date deadline;


    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "删除状态")
    @TableLogic(value = "0",delval = "1")
    private Integer delFlag;

    /** 创建者 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新者 */
    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
