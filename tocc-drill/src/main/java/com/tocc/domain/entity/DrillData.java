package com.tocc.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("drill_data")
public class DrillData  {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "事故情景描述")
    private String accidentSceneDescription;


    @ApiModelProperty(value = "演练目标达成情况")
    private String drillAchievement;

    @ApiModelProperty(value = "暴露问题和薄弱环节")
    private String question;


    @ApiModelProperty(value = "改进措施")
    private String improve;


    @ApiModelProperty(value = "演练地点")
    private String address;



    @ApiModelProperty(value = "演练照片")
    private String photo;

    @ApiModelProperty(value = "演练签到表")
    private String signInSheet;


    @ApiModelProperty(value = "附件")
    private String annex;

    @ApiModelProperty(value = "关联演练计划id")
    private Long drillPlanId;

    @ApiModelProperty(value = "填报人")
    private String reporter;


    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "删除状态")
    @TableLogic(value = "0",delval = "1")
    private Integer delFlag;

    /** 创建者 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新者 */
    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;
}
