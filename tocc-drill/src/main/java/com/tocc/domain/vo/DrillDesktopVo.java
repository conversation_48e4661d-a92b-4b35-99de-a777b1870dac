package com.tocc.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DrillDesktopVo {

    private Long id;

    @ApiModelProperty(value = "演练方式")
    private String drillWay;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "演练名称")
    private String drillName;


    @ApiModelProperty(value = "演练规模")
    private String drillScale;

    @ApiModelProperty(value = "主办单位")
    private String organizer;

    @ApiModelProperty(value = "事故情景描述")
    private String accidentSceneDescription;

    private String status;
}
