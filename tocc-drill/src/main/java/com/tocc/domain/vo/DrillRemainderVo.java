package com.tocc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DrillRemainderVo {
    private Long id;

    private String drillName;

    private String organizer;

    private String responsible;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date drillDate;

    @ApiModelProperty("提醒上交演练资料 N/Y")
    private String isDrill;

    @ApiModelProperty("提醒上交复盘报告 N/Y")
    private String isReview;

    @ApiModelProperty(value = "关联演练计划id")
    private Long drillPlanId;

    @ApiModelProperty(value = "截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadline;
}
