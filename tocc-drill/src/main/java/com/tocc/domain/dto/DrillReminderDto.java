package com.tocc.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DrillReminderDto {


    private Long id;

    private Long drillPlanId;


    @ApiModelProperty("提醒上交演练资料 N/Y")
    private String isDrill;

    @ApiModelProperty("提醒上交复盘报告 N/Y")
    private String isReview;

    @ApiModelProperty(value = "截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadline;
}
