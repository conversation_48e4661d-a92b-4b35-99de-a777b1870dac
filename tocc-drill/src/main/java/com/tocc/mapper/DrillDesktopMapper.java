package com.tocc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tocc.domain.dto.DrillDesktopDto;
import com.tocc.domain.dto.DrillPlanDto;
import com.tocc.domain.entity.DrillDesktop;
import com.tocc.domain.vo.DrillDesktopVo;
import com.tocc.domain.vo.DrillPlanVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DrillDesktopMapper extends BaseMapper<DrillDesktop> {

    List<DrillDesktopVo> list(DrillDesktopDto dto);
}
