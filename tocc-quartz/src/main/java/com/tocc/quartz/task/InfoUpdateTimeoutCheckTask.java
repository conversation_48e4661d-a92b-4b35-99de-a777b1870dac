package com.tocc.quartz.task;

import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.em.service.IEmPrePlanService;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.service.IAlarmService;
import com.tocc.system.service.ISysDictDataService;
import com.tocc.system.service.ISysUserService;
import com.tocc.system.service.ISysDeptService;
import com.tocc.mapper.AlarmInfoMapper;
import com.tocc.domain.vo.YkTokenVO;
import com.google.gson.Gson;
import com.tocc.utils.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 应急预案信息更新超时检查定时任务
 *
 * <AUTHOR>
 */
@Component("infoUpdateTimeoutCheckTask")
public class InfoUpdateTimeoutCheckTask {

    private static final Logger log = LoggerFactory.getLogger(InfoUpdateTimeoutCheckTask.class);

    @Autowired
    private IAlarmService alarmService;

    @Autowired
    private AlarmInfoMapper alarmInfoMapper;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IEmPrePlanService prePlanService;
    
    /**
     * 执行应急预案信息更新超时检查（无参版本）
     */
    public void execute() {
        execute(null);
    }

    /**
     * 执行应急预案信息更新超时检查
     *
     * @param params 参数（可选）
     */
    public void execute(String params) {
        log.info("开始执行应急预案信息更新超时检查任务，参数：{}", params);

        try {
            // 检查应急预案
            checkPrePlanTimeout();

            log.info("应急预案信息更新超时检查任务执行完成");
        } catch (Exception e) {
            log.error("应急预案信息更新超时检查任务执行失败", e);
            throw e;
        }
    }
    
    /**
     * 检查应急预案更新超时
     */
    private void checkPrePlanTimeout() {
        // 获取超时阈值（分钟）
        int timeoutMinutes = getTimeoutMinutes("应急预案更新阈值", 1);
        
        // 计算超时时间点
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        
        // 查询超时的预案
        List<EmPrePlanVO> timeoutPlans = prePlanService.selectTimeoutPlans(timeoutTime);
        
        log.info("发现{}个超时的应急预案", timeoutPlans.size());
        
        // 为每个超时预案创建告警
        for (EmPrePlanVO plan : timeoutPlans) {
            createTimeoutAlarm("3", plan.getId(), plan.getPlanName(),
                              plan.getLastCheckTime(), timeoutMinutes, plan.getCreator());

            // 发送短信通知预案创建人
            sendSmsToCreator(plan, timeoutMinutes);
        }
    }

    /**
     * 获取超时阈值（分钟）
     */
    private int getTimeoutMinutes(String dictLabel, int defaultValue) {
        try {
            String dictValue = dictDataService.selectDictLabel("info_update_timeout", dictLabel);
            if (StringUtils.isNotEmpty(dictValue)) {
                return Integer.parseInt(dictValue);
            }
        } catch (Exception e) {
            log.warn("获取字典值失败，使用默认值：{}", defaultValue, e);
        }
        return defaultValue;
    }
    
    /**
     * 创建超时告警
     */
    private void createTimeoutAlarm(String infoType, String infoId, String infoName,
                                   Date lastUpdateTime, int timeoutMinutes, String creatorName) {
        
        // 检查是否已经存在相同的告警（避免重复告警）
        if (alarmService.existsTimeoutAlarm(infoType, infoId)) {
            log.debug("{}[{}]已存在超时告警，跳过", infoType, infoName);
            return;
        }
        
        // 计算超时分钟数
        long overdueMinutes = ChronoUnit.MINUTES.between(
            lastUpdateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
            LocalDateTime.now()
        ) - timeoutMinutes;
        
        // 获取中文类型名称用于显示
        String infoTypeName = getInfoTypeName(infoType);

        // 构建告警标题
        String alarmTitle = String.format("%s信息更新超时", infoTypeName);

        // 构建告警内容
        String alarmContent = String.format(
            "%s\"%s\"已超过%d分钟未更新信息，当前已超时%d分钟。最后更新时间：%s。请及时更新相关信息以确保数据准确性。",
            infoTypeName, infoName, timeoutMinutes, overdueMinutes,
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, lastUpdateTime)
        );
        
        // 创建告警记录
        AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
        alarmInfo.setAlarmId(generateAlarmId()); // 生成唯一告警ID
        alarmInfo.setAlarmType("2"); // 信息更新超时对应字典值2
        alarmInfo.setAlarmSubtype(getAlarmSubtype(infoType)); // 设置告警子类型
        alarmInfo.setAlarmTitle(alarmTitle);
        alarmInfo.setAlarmContent(alarmContent);
        alarmInfo.setAlarmLevel("2"); // 中等级别
        alarmInfo.setSourceType(infoType); // 源数据类型
        alarmInfo.setSourceId(infoId); // 源数据ID

        // 获取创建人的部门信息
        String[] orgInfo = getCreatorOrgInfo(creatorName);
        alarmInfo.setOrgId(orgInfo[0]); // 设置组织ID
        alarmInfo.setOrgName(orgInfo[1]); // 设置组织名称
        alarmInfo.setAdministrativeAreaId("default"); // 设置默认行政区划ID
        alarmInfo.setAlarmTime(new Date());
        alarmInfo.setStatus("0"); // 未处理
        alarmInfo.setCreateBy("system"); // 系统创建
        alarmInfo.setCreateTime(new Date());

        // 直接调用mapper插入，避免使用SecurityUtils
        alarmInfoMapper.insertAlarmInfo(alarmInfo);
        
        log.info("创建{}更新超时告警：{}", infoTypeName, infoName);
    }

    /**
     * 根据信息类型获取告警子类型
     *
     * @param infoType 信息类型
     * @return 告警子类型
     */
    private String getAlarmSubtype(String infoType) {
        if ("3".equals(infoType)) { // 应急预案
            return "4"; // 预案更新超时
        }
        return "4"; // 默认为预案更新超时
    }

    /**
     * 根据信息类型字典值获取中文名称
     *
     * @param infoType 信息类型字典值
     * @return 中文名称
     */
    private String getInfoTypeName(String infoType) {
        if ("3".equals(infoType)) {
            return "应急预案";
        }
        return "应急预案"; // 默认为应急预案
    }

    /**
     * 根据创建人获取组织信息
     *
     * @param creatorName 创建人姓名
     * @return 组织信息数组 [组织ID, 组织名称]
     */
    private String[] getCreatorOrgInfo(String creatorName) {
        String[] defaultOrgInfo = {"default", "默认部门"};

        if (StringUtils.isEmpty(creatorName)) {
            return defaultOrgInfo;
        }

        try {
            // 根据创建人姓名查询用户信息
            SysUser user = userService.selectUserByUserName(creatorName);
            if (user == null || user.getDeptId() == null) {
                log.warn("未找到创建人[{}]的用户信息或部门信息", creatorName);
                return defaultOrgInfo;
            }

            // 根据部门ID查询部门信息
            SysDept dept = deptService.selectDeptById(user.getDeptId());
            if (dept == null) {
                log.warn("未找到部门信息，部门ID: {}", user.getDeptId());
                return defaultOrgInfo;
            }

            return new String[]{dept.getDeptId().toString(), dept.getDeptName()};

        } catch (Exception e) {
            log.error("获取创建人[{}]的组织信息失败", creatorName, e);
            return defaultOrgInfo;
        }
    }

    /**
     * 生成唯一的告警ID
     *
     * @return 告警ID
     */
    private String generateAlarmId() {
        return IdUtils.fastUUID();
    }

    /**
     * 发送短信通知应急预案创建人
     *
     * @param plan 应急预案信息
     * @param timeoutMinutes 超时分钟数
     */
    private void sendSmsToCreator(EmPrePlanVO plan, int timeoutMinutes) {
        try {
            // 根据创建人姓名查询用户信息获取手机号
            SysUser user = userService.selectUserByUserName(plan.getCreator());
            if (user == null || StringUtils.isEmpty(user.getPhonenumber())) {
                log.warn("应急预案创建人手机号为空，无法发送短信通知，预案ID: {}, 创建人: {}",
                        plan.getId(), plan.getCreator());
                return;
            }

            String creatorMobile = user.getPhonenumber();

            // 构建短信内容
            String smsContent = buildPrePlanSmsContent(plan, timeoutMinutes);

            // 获取token并发送短信
            String token = getYkToken();
            if (token != null) {
                smsSend(token, creatorMobile, smsContent);
                log.info("应急预案超时告警短信发送成功，手机号: {}, 内容: {}", creatorMobile, smsContent);
            } else {
                log.error("获取短信token失败，无法发送应急预案超时告警短信");
            }
        } catch (Exception e) {
            // 短信发送失败不影响主业务流程，只记录日志
            log.error("发送应急预案超时告警短信失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建应急预案超时告警短信内容（JSON格式）
     *
     * @param plan 应急预案信息
     * @param timeoutMinutes 超时分钟数
     * @return 短信内容（JSON格式）
     */
    private String buildPrePlanSmsContent(EmPrePlanVO plan, int timeoutMinutes) {
        // 构建date字段（格式：2025年06月03日11时22分30秒）
        String dateStr = DateUtils.parseDateToStr("yyyy年MM月dd日HH时mm分ss秒", new Date());

        // 构建type字段
        String typeStr = "应急预案";

        // 构建content字段（参考告警内容格式）
        long overdueMinutes = ChronoUnit.MINUTES.between(
            plan.getLastCheckTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
            LocalDateTime.now()
        );

        String contentStr = String.format(
            "应急预案\"%s\"已超过%d分钟未更新信息，当前已超时%d分钟。最后更新时间：%s。" +
            "预案类型：%s，适用范围：%s。请及时更新相关信息以确保数据准确性。",
            plan.getPlanName(), timeoutMinutes, overdueMinutes,
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, plan.getLastCheckTime()),
            plan.getPlanType() != null ? plan.getPlanType() : "未填写",
            plan.getScope() != null ? plan.getScope() : "未填写"
        );

        // 对JSON字符串中的特殊字符进行转义
        String escapedContent = escapeJsonString(contentStr);

        // 构建JSON格式的内容
        return String.format("{\"date\":\"%s\",\"type\":\"%s\",\"content\":\"%s\"}",
                dateStr, typeStr, escapedContent);
    }

    /**
     * 转义JSON字符串中的特殊字符
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    private String escapeJsonString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\\", "\\\\")  // 转义反斜杠
                  .replace("\"", "\\\"")  // 转义双引号
                  .replace("\b", "\\b")   // 转义退格符
                  .replace("\f", "\\f")   // 转义换页符
                  .replace("\n", "\\n")   // 转义换行符
                  .replace("\r", "\\r")   // 转义回车符
                  .replace("\t", "\\t");  // 转义制表符
    }

    /**
     * 获取云控平台Token
     *
     * @return Token字符串
     */
    private String getYkToken() {
        try {
            String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken";
            Map<String, String> body = new HashMap<>();
            body.put("appId", "fb9686ae-8706-46ce-926d-23ddfbc010e9");
            body.put("appSecret", "WTKkpkH*wAyFCKgbbfNz$sMnbuqd#Wj#");
            String response = HttpClientUtils.postWithBody(10000, url, null, body);
            YkTokenVO vo = new Gson().fromJson(response, YkTokenVO.class);
            if (vo.getCode() == 1) {
                return vo.getToken();
            }
            log.error("获取云控平台Token失败，响应: {}", response);
            return null;
        } catch (Exception e) {
            log.error("获取云控平台Token异常", e);
            return null;
        }
    }

    /**
     * 发送短信
     *
     * @param token Token
     * @param mobile 手机号
     * @param content 短信内容
     */
    private void smsSend(String token, String mobile, String content) {
        try {
            String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend";
            Map<String, String> head = new HashMap<>();
            head.put("AuthorizationType", "other");
            head.put("Authorization", token);
            Map<String, String> body = new HashMap<>();
            body.put("mobile", mobile);
            body.put("content", content);
            body.put("signName", "智慧高速云控平台");
            body.put("templateKey", "UPDATE_EMER_EVENT");
            String response = HttpClientUtils.postWithBody(10000, url, head, body);
            log.info("应急预案超时告警短信发送响应: {}", response);
        } catch (Exception e) {
            log.error("发送应急预案超时告警短信异常", e);
        }
    }
}
