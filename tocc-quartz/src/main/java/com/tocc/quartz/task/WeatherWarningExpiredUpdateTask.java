package com.tocc.quartz.task;

import com.tocc.weather.service.IWeatherWarningService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 气象预警过期状态更新定时任务
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Component("weatherWarningExpiredUpdateTask")
public class WeatherWarningExpiredUpdateTask {

    private static final Logger log = LoggerFactory.getLogger(WeatherWarningExpiredUpdateTask.class);

    @Autowired
    private IWeatherWarningService weatherWarningService;

    /**
     * 执行气象预警过期状态更新（无参版本）
     */
    public void execute() {
        execute(null);
    }

    /**
     * 执行气象预警过期状态更新
     *
     * @param params 参数（可选）
     */
    public void execute(String params) {
        log.info("开始执行气象预警过期状态更新任务，参数：{}", params);

        try {
            // 调用Service层的手动更新方法
            int updatedCount = weatherWarningService.manualUpdateExpiredWarnings();

            if (updatedCount > 0) {
                log.info("成功更新 {} 条过期预警状态为已失效", updatedCount);
            } else {
                log.info("没有发现过期的有效预警");
            }

            log.info("气象预警过期状态更新任务执行完成");
        } catch (Exception e) {
            log.error("气象预警过期状态更新任务执行失败", e);
            throw e;
        }
    }
}
