package com.tocc.service.impl;

import com.tocc.common.annotation.DataScope;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.vo.AlarmInfoVO;
import com.tocc.mapper.AlarmInfoMapper;
import com.tocc.service.IAlarmService;
import com.tocc.system.service.ISysDictDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 告警信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class AlarmServiceImpl implements IAlarmService {

    private static final Logger log = LoggerFactory.getLogger(AlarmServiceImpl.class);

    @Autowired
    private AlarmInfoMapper alarmInfoMapper;

    /**
     * 查询告警信息
     * 
     * @param alarmId 告警信息主键
     * @return 告警信息
     */
    @Override
    public AlarmInfoVO selectAlarmInfoByAlarmId(String alarmId) {
        return alarmInfoMapper.selectAlarmInfoByAlarmId(alarmId);
    }

    /**
     * 查询告警信息列表
     *
     * @param alarmInfo 告警信息
     * @return 告警信息
     */
    @Override
    @DataScope(deptAlias = "a")
    public List<AlarmInfoVO> selectAlarmInfoList(AlarmInfoDTO alarmInfo) {
        return alarmInfoMapper.selectAlarmInfoList(alarmInfo);
    }

    /**
     * 新增告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    @Override
    public int insertAlarmInfo(AlarmInfoDTO alarmInfo) {
        // 设置默认值
        if (alarmInfo.getAlarmId() == null || alarmInfo.getAlarmId().trim().isEmpty()) {
            alarmInfo.setAlarmId(IdUtils.fastSimpleUUID());
        }
        if (alarmInfo.getAlarmTime() == null) {
            alarmInfo.setAlarmTime(new Date());
        }
        if (alarmInfo.getStatus() == null || alarmInfo.getStatus().trim().isEmpty()) {
            alarmInfo.setStatus("0"); // 默认未处理
        }
        
        alarmInfo.setCreateBy(SecurityUtils.getUsername());
        alarmInfo.setCreateTime(DateUtils.getNowDate());
        
        return alarmInfoMapper.insertAlarmInfo(alarmInfo);
    }

    /**
     * 修改告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    @Override
    public int updateAlarmInfo(AlarmInfoDTO alarmInfo) {
        alarmInfo.setUpdateBy(SecurityUtils.getUsername());
        alarmInfo.setUpdateTime(DateUtils.getNowDate());
        return alarmInfoMapper.updateAlarmInfo(alarmInfo);
    }

    /**
     * 批量删除告警信息
     * 
     * @param alarmIds 需要删除的告警信息主键
     * @return 结果
     */
    @Override
    public int deleteAlarmInfoByAlarmIds(String[] alarmIds) {
        return alarmInfoMapper.deleteAlarmInfoByAlarmIds(alarmIds);
    }

    /**
     * 删除告警信息信息
     * 
     * @param alarmId 告警信息主键
     * @return 结果
     */
    @Override
    public int deleteAlarmInfoByAlarmId(String alarmId) {
        return alarmInfoMapper.deleteAlarmInfoByAlarmId(alarmId);
    }

    /**
     * 处理告警
     * 
     * @param alarmId 告警ID
     * @param status 处理状态（1已处理 2已忽略）
     * @param processResult 处理结果
     * @return 结果
     */
    @Override
    public int processAlarm(String alarmId, String status, String processResult) {
        String processorId = SecurityUtils.getUserId().toString();
        String processorName = SecurityUtils.getUsername();
        String updateBy = SecurityUtils.getUsername();
        
        return alarmInfoMapper.updateAlarmStatus(alarmId, status, processorId, 
                                               processorName, processResult, updateBy);
    }

    /**
     * 统计告警数量
     * 
     * @param alarmInfo 查询条件
     * @return 统计数量
     */
    @Override
    public int countAlarmInfo(AlarmInfoDTO alarmInfo) {
        return alarmInfoMapper.countAlarmInfo(alarmInfo);
    }



    /**
     * 检查是否存在相同的超时告警
     *
     * @param infoType 信息类型
     * @param infoId 信息ID
     * @return 是否存在
     */
    @Override
    public boolean existsTimeoutAlarm(String infoType, String infoId) {
        // 查询最近7天内是否已有相同的超时告警
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);

        return alarmInfoMapper.existsTimeoutAlarm(infoType, infoId, sevenDaysAgo);
    }

    /**
     * 创建告警
     *
     * @param alarmInfo 告警信息
     * @return 结果
     */
    @Override
    public int createAlarm(AlarmInfoDTO alarmInfo) {
        return insertAlarmInfo(alarmInfo);
    }

    /**
     * 获取告警数据源详情
     *
     * @param alarmId 告警ID
     * @return 数据源详情
     */
    @Override
    public Map<String, Object> getAlarmSourceDetail(String alarmId) {
        // 查询告警信息
        AlarmInfoVO alarmInfo = alarmInfoMapper.selectAlarmInfoByAlarmId(alarmId);
        if (alarmInfo == null) {
            return null;
        }

        String sourceType = alarmInfo.getSourceType();
        String sourceId = alarmInfo.getSourceId();

        Map<String, Object> result = new HashMap<>();
        result.put("sourceType", sourceType);
        result.put("sourceId", sourceId);

        // 根据源类型获取对应的详情
        switch (sourceType) {
            case "2": // 应急事件
                Map<String, Object> eventDetail = getEmergencyEventDetail(sourceId);
                result.put("sourceDetail", eventDetail);
                result.put("sourceTypeName", "应急事件");
                break;
            case "6": // 气象预警
                Map<String, Object> warningDetail = getWeatherWarningDetail(sourceId);
                result.put("sourceDetail", warningDetail);
                result.put("sourceTypeName", "气象预警");
                break;
            case "1": // 隐患
                // TODO: 待隐患模块负责人实现
                result.put("sourceDetail", createTodoDetail("隐患详情接口待实现"));
                result.put("sourceTypeName", "隐患");
                break;
            case "3": // 应急预案
                // TODO: 待应急预案模块负责人实现
                result.put("sourceDetail", createTodoDetail("应急预案详情接口待实现"));
                result.put("sourceTypeName", "应急预案");
                break;
            case "4": // 应急物资
                // TODO: 待应急物资模块负责人实现
                result.put("sourceDetail", createTodoDetail("应急物资详情接口待实现"));
                result.put("sourceTypeName", "应急物资");
                break;
            case "5": // 应急通讯录
                // TODO: 待应急通讯录模块负责人实现
                result.put("sourceDetail", createTodoDetail("应急通讯录详情接口待实现"));
                result.put("sourceTypeName", "应急通讯录");
                break;
            case "7": // 应急救援队伍
                // TODO: 待应急救援队伍模块负责人实现
                result.put("sourceDetail", createTodoDetail("应急救援队伍详情接口待实现"));
                result.put("sourceTypeName", "应急救援队伍");
                break;
            default:
                result.put("sourceDetail", createTodoDetail("未知的数据源类型"));
                result.put("sourceTypeName", "未知");
                break;
        }

        return result;
    }

    /**
     * 获取应急事件详情
     *
     * @param eventId 事件ID
     * @return 事件详情
     */
    private Map<String, Object> getEmergencyEventDetail(String eventId) {
        // TODO: 调用应急事件模块的详情接口
        // 这里需要注入应急事件的Service，然后调用其详情查询方法
        Map<String, Object> detail = new HashMap<>();
        detail.put("eventId", eventId);
        detail.put("message", "应急事件详情接口待实现");
        detail.put("todo", "请应急事件模块负责人实现此方法");
        return detail;
    }

    /**
     * 获取气象预警详情
     *
     * @param warningId 预警ID
     * @return 预警详情
     */
    private Map<String, Object> getWeatherWarningDetail(String warningId) {
        try {
            log.info("开始根据告警查询气象预警详情：warningId={}", warningId);

            // 获取当前用户信息
            Long currentUserId = SecurityUtils.getUserId();
            String currentUsername = SecurityUtils.getUsername();
            Long orgId = SecurityUtils.getLoginUser().getUser().getOrgId();
            String currentOrgId = orgId != null ? orgId.toString() : null;

            // 查询预警信息（包含统计）
            Map<String, Object> warning = alarmInfoMapper.selectWeatherWarningWithStats(warningId);
            if (warning == null) {
                log.warn("预警不存在：warningId={}", warningId);
                return createErrorDetail("气象预警信息不存在");
            }

            // 获取用户通知记录
            Map<String, Object> userNotification = alarmInfoMapper.selectWeatherWarningNotificationByIds(warningId, currentUserId);

            // 权限检查：判断用户是否有权限查看该预警
            boolean hasPermission = checkUserPermissionForWarning(warning, currentUsername, currentUserId, currentOrgId, userNotification);
            if (!hasPermission) {
                log.warn("用户无权限查看预警详情：userId={}, warningId={}", currentUserId, warningId);
                return createErrorDetail("无权限查看该预警详情");
            }

            // 构建预警详情VO
            Map<String, Object> warningVO = buildWeatherWarningVO(warning, currentUsername, currentUserId, currentOrgId, userNotification);
            return warningVO;

        } catch (Exception e) {
            log.error("查询气象预警详情失败：warningId={}", warningId, e);
            return createErrorDetail("查询预警详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建TODO详情对象
     *
     * @param message 提示信息
     * @return TODO详情
     */
    private Map<String, Object> createTodoDetail(String message) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("message", message);
        detail.put("status", "TODO");
        return detail;
    }

    /**
     * 创建错误详情对象
     *
     * @param message 错误消息
     * @return 错误详情
     */
    private Map<String, Object> createErrorDetail(String message) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("error", true);
        detail.put("message", message);
        return detail;
    }

    /**
     * 检查用户是否有权限查看指定预警
     * 权限规则：
     * 1. 预警创建者：立即可见
     * 2. 被通知用户：被通知后可见
     * 3. 同单位/上级单位用户：在有用户被通知后可见
     *
     * @param warning 预警信息
     * @param currentUsername 当前用户名
     * @param currentUserId 当前用户ID
     * @param currentOrgId 当前用户单位ID
     * @param userNotification 用户通知记录（可能为null）
     * @return 是否有权限
     */
    private boolean checkUserPermissionForWarning(Map<String, Object> warning, String currentUsername, Long currentUserId, String currentOrgId, Map<String, Object> userNotification) {
        try {
            // 1. 检查是否为创建者
            String createBy = (String) warning.get("create_by");
            if (currentUsername.equals(createBy)) {
                log.debug("用户是预警创建者，有权限查看：warningId={}", warning.get("warning_id"));
                return true;
            }

            // 2. 检查是否被通知
            if (userNotification != null) {
                log.debug("用户被通知该预警，有权限查看：warningId={}", warning.get("warning_id"));
                return true;
            }

            // 3. 检查单位可见性（同单位/上级单位在有用户被通知后可见）
            if (StringUtils.isNotEmpty(currentOrgId)) {
                // 查询该预警是否有本单位的用户被通知
                List<Map<String, Object>> orgNotifications = alarmInfoMapper.selectNotificationsByWarningIdAndOrgId((String) warning.get("warning_id"), currentOrgId);
                if (!orgNotifications.isEmpty()) {
                    log.debug("用户单位有人被通知该预警，有权限查看：warningId={}, orgId={}", warning.get("warning_id"), currentOrgId);
                    return true;
                }

                // 查询是否为单位创建的预警（单位及上层单位创建的预警）
                List<Map<String, Object>> orgCreatedWarnings = alarmInfoMapper.selectWarningsByOrgId(currentOrgId);
                boolean isOrgCreated = orgCreatedWarnings.stream()
                        .anyMatch(w -> w.get("warning_id").equals(warning.get("warning_id")));
                if (isOrgCreated) {
                    log.debug("预警由用户单位创建，有权限查看：warningId={}, orgId={}", warning.get("warning_id"), currentOrgId);
                    return true;
                }
            }

            log.debug("用户无权限查看预警：warningId={}, userId={}", warning.get("warning_id"), currentUserId);
            return false;

        } catch (Exception e) {
            log.error("检查预警权限失败：warningId={}, userId={}", warning.get("warning_id"), currentUserId, e);
            return false;
        }
    }

    /**
     * 构建气象预警VO
     *
     * @param warning 预警信息
     * @param currentUsername 当前用户名
     * @param currentUserId 当前用户ID
     * @param currentOrgId 当前用户单位ID
     * @param userNotification 用户通知记录
     * @return 预警VO
     */
    private Map<String, Object> buildWeatherWarningVO(Map<String, Object> warning, String currentUsername, Long currentUserId, String currentOrgId, Map<String, Object> userNotification) {
        Map<String, Object> vo = new HashMap<>();

        try {
            // 基础信息
            vo.put("warningId", warning.get("warning_id"));
            vo.put("warningType", warning.get("warning_type"));
            vo.put("warningLevel", warning.get("warning_level"));
            vo.put("warningContent", warning.get("warning_content"));
            vo.put("preventionGuide", warning.get("prevention_guide"));
            vo.put("affectedRoads", warning.get("affected_roads"));

            // 格式化时间字段并添加调试日志
            Object issueTimeRaw = warning.get("issue_time");
            String issueTimeFormatted = formatDateTime(issueTimeRaw);
            log.info("时间格式化调试 - issueTime: {} -> {}", issueTimeRaw, issueTimeFormatted);
            vo.put("issueTime", issueTimeFormatted);

            Object expireTimeRaw = warning.get("expire_time");
            String expireTimeFormatted = formatDateTime(expireTimeRaw);
            log.info("时间格式化调试 - expireTime: {} -> {}", expireTimeRaw, expireTimeFormatted);
            vo.put("expireTime", expireTimeFormatted);

            vo.put("status", warning.get("status"));
            vo.put("isNotified", warning.get("is_notified"));

            Object createTimeRaw = warning.get("create_time");
            String createTimeFormatted = formatDateTime(createTimeRaw);
            log.info("时间格式化调试 - createTime: {} -> {}", createTimeRaw, createTimeFormatted);
            vo.put("createTime", createTimeFormatted);

            vo.put("createBy", warning.get("create_by"));

            // 添加标签信息
            vo.put("warningTypeLabel", getWarningTypeLabel(getStringValue(warning, "warning_type")));
            vo.put("warningLevelLabel", getWarningLevelLabel(getStringValue(warning, "warning_level")));
            vo.put("statusLabel", getStatusLabel(getStringValue(warning, "status")));
            vo.put("isNotifiedLabel", getIsNotifiedLabel(getBooleanStringValue(warning, "is_notified")));

            // 统计信息
            vo.put("totalNotifications", getIntValue(warning, "total_notifications"));
            vo.put("confirmedNotifications", getIntValue(warning, "confirmed_notifications"));
            vo.put("unconfirmedNotifications", getIntValue(warning, "unconfirmed_notifications"));
            vo.put("timeoutNotifications", getIntValue(warning, "timeout_notifications"));

            // 查询影响区域
            List<Map<String, Object>> areas = alarmInfoMapper.selectWeatherWarningAreas((String) warning.get("warning_id"));
            // 格式化区域数据中的时间字段
            formatTimeFieldsInList(areas, "create_time");
            vo.put("affectedAreas", areas);
            vo.put("affectedAreasDesc", buildAreaDescription(areas));

            // 设置用户权限相关字段
            setUserPermissionsForWarning(vo, warning, currentUsername, currentUserId, currentOrgId, userNotification);

            // 设置通知状态和确认状态
            String notificationStatus = calculateNotificationStatus(warning);
            vo.put("notificationStatus", notificationStatus);

            String confirmStatus = calculateConfirmStatus(userNotification);
            vo.put("confirmStatus", confirmStatus);

            // 设置是否可以通知（只有org_id为100的用户才能通知）
            boolean canNotify = canUserNotify(currentOrgId);
            vo.put("canNotify", canNotify);

            // 根据权限设置通知进展信息
            if (getBooleanValue(warning, "is_notified")) {
                Boolean canViewProgress = (Boolean) vo.get("canViewProgress");
                if (Boolean.TRUE.equals(canViewProgress)) {
                    // 有权限查看通知进度详情
                    List<Map<String, Object>> progressList = alarmInfoMapper.selectNotificationProgress((String) warning.get("warning_id"));
                    // 格式化通知进度中的时间字段
                    formatTimeFieldsInList(progressList, "notification_time", "confirm_time");
                    vo.put("notificationProgress", progressList);
                } else {
                    // 无权限查看详细进度，只显示基本统计
                    vo.put("notificationProgress", new ArrayList<>());
                }
            }

            return vo;

        } catch (Exception e) {
            log.error("构建预警VO失败：warningId={}", warning.get("warning_id"), e);
            return createErrorDetail("构建预警详情失败：" + e.getMessage());
        }
    }

    /**
     * 为预警VO设置用户权限相关字段
     *
     * @param vo 预警VO
     * @param warning 预警信息
     * @param currentUsername 当前用户名
     * @param currentUserId 当前用户ID
     * @param currentOrgId 当前用户单位ID
     * @param userNotification 用户通知记录（可能为null）
     */
    private void setUserPermissionsForWarning(Map<String, Object> vo, Map<String, Object> warning, String currentUsername, Long currentUserId, String currentOrgId, Map<String, Object> userNotification) {
        try {
            // 1. 确定用户角色
            String userRole = "viewer"; // 默认为查看者
            boolean isCreator = currentUsername.equals(warning.get("create_by"));
            boolean isNotified = false;
            boolean isOrgCreator = false;

            // 检查是否被通知（使用传入的通知记录）
            if (userNotification != null) {
                isNotified = true;
                log.info("用户被通知该预警：warningId={}, userId={}", warning.get("warning_id"), currentUserId);
            } else {
                log.info("用户未被通知该预警：warningId={}, userId={}", warning.get("warning_id"), currentUserId);
            }

            // 检查是否为单位创建者
            if (!isCreator && StringUtils.isNotEmpty(currentOrgId)) {
                List<Map<String, Object>> orgCreatedWarnings = alarmInfoMapper.selectWarningsByOrgId(currentOrgId);
                isOrgCreator = orgCreatedWarnings.stream()
                        .anyMatch(w -> w.get("warning_id").equals(warning.get("warning_id")));
            }

            // 确定用户角色
            if (isCreator) {
                userRole = "creator";
            } else if (isOrgCreator) {
                userRole = "org_creator";
            } else if (isNotified) {
                userRole = "notified";
            } else {
                userRole = "org_viewer";
            }

            vo.put("alarmType", userRole);

            // 2. 设置是否可以确认（只有被直接通知的用户才能确认）
            vo.put("canConfirm", isNotified);

            // 3. 设置是否可以查看通知进度详情（创建者和单位创建者可以查看）
            vo.put("canViewProgress", isCreator || isOrgCreator);

            // 4. 设置当前用户的确认状态（如果被通知）
            if (isNotified && userNotification != null) {
                vo.put("currentUserConfirmStatus", userNotification.get("confirm_status"));
                vo.put("currentUserConfirmTime", userNotification.get("confirm_time"));
            }

            log.debug("设置用户权限：warningId={}, userId={}, userRole={}, canConfirm={}, canViewProgress={}",
                    warning.get("warning_id"), currentUserId, userRole, vo.get("canConfirm"), vo.get("canViewProgress"));

        } catch (Exception e) {
            log.error("设置用户权限失败：warningId={}, userId={}", warning.get("warning_id"), currentUserId, e);
            // 设置默认权限（最小权限）
            vo.put("alarmType", "viewer");
            vo.put("canConfirm", false);
            vo.put("canViewProgress", false);
        }
    }

    /**
     * 计算通知状态
     * @param warning 预警信息
     * @return 通知状态（0-未通知，1-通知未被全部确认，2-通知已确认）
     */
    private String calculateNotificationStatus(Map<String, Object> warning) {
        Boolean isNotified = getBooleanValue(warning, "is_notified");
        if (!isNotified) {
            return "0"; // 未通知
        }

        // 已通知，检查确认情况
        Integer totalNotifications = getIntValue(warning, "total_notifications");
        Integer confirmedNotifications = getIntValue(warning, "confirmed_notifications");

        if (totalNotifications == 0) {
            return "0"; // 未通知
        }

        if (confirmedNotifications.equals(totalNotifications)) {
            return "2"; // 通知已确认
        } else {
            return "1"; // 通知未被全部确认
        }
    }



    /**
     * 计算当前用户的确认状态
     * @param userNotification 用户通知记录
     * @return 确认状态（0-未确认，1-已确认）
     */
    private String calculateConfirmStatus(Map<String, Object> userNotification) {
        if (userNotification == null) {
            return "0"; // 未确认（未被通知）
        }
        Object confirmStatus = userNotification.get("confirm_status");
        return confirmStatus != null ? confirmStatus.toString() : "0";
    }

    /**
     * 检查用户是否可以进行通知操作
     * 只有org_id为100的用户才能进行通知
     *
     * @param currentOrgId 当前用户的单位ID
     * @return 是否可以通知
     */
    private boolean canUserNotify(String currentOrgId) {
        return "100".equals(currentOrgId);
    }

    /**
     * 格式化日期时间
     *
     * @param dateTime 日期时间对象
     * @return 格式化后的字符串 (yyyy-MM-dd HH:mm:ss)
     */
    private String formatDateTime(Object dateTime) {
        if (dateTime == null) {
            return null;
        }

        if (dateTime instanceof Date) {
            String result = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, (Date) dateTime);
            return result;
        } else if (dateTime instanceof java.sql.Timestamp) {
            String result = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date(((java.sql.Timestamp) dateTime).getTime()));
            return result;
        } else if (dateTime instanceof LocalDateTime) {
            // 处理LocalDateTime类型
            LocalDateTime localDateTime = (LocalDateTime) dateTime;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String result = localDateTime.format(formatter);
            return result;
        } else if (dateTime instanceof String) {
            String dateTimeStr = dateTime.toString();
            // 处理ISO 8601格式 (如: 2025-06-17T16:20:04)
            if (dateTimeStr.contains("T")) {
                try {
                    // 替换T为空格，处理ISO格式
                    String normalizedStr = dateTimeStr.replace("T", " ");
                    // 如果没有秒，补充秒
                    if (normalizedStr.length() == 16) { // yyyy-MM-dd HH:mm
                        normalizedStr += ":00";
                    }

                    // 使用SimpleDateFormat直接解析
                    try {
                        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date = inputFormat.parse(normalizedStr);
                        String result = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, date);
                        return result;
                    } catch (Exception parseEx) {
                    }

                    // 备用方案：使用DateUtils
                    Date date = DateUtils.parseDate(normalizedStr);
                    if (date != null) {
                        String result = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, date);
                        return result;
                    }
                } catch (Exception e) {
                }
            }

            // 尝试其他格式解析
            try {
                Date date = DateUtils.parseDate(dateTimeStr);
                if (date != null) {
                    String result = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, date);
                    return result;
                }
            } catch (Exception e) {
            }

            return dateTimeStr;
        }

        return dateTime.toString();
    }

    /**
     * 批量格式化List中Map对象的时间字段
     *
     * @param dataList 数据列表
     * @param timeFields 需要格式化的时间字段名
     */
    private void formatTimeFieldsInList(List<Map<String, Object>> dataList, String... timeFields) {
        if (dataList == null || dataList.isEmpty() || timeFields == null) {
            return;
        }

        for (Map<String, Object> data : dataList) {
            for (String timeField : timeFields) {
                if (data.containsKey(timeField)) {
                    Object timeValue = data.get(timeField);
                    String formattedTime = formatDateTime(timeValue);
                    data.put(timeField, formattedTime);
                }
            }
        }
    }



    /**
     * 从Map中安全获取Integer值
     */
    private Integer getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }

    /**
     * 从Map中安全获取String值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return "";
        }
        return value.toString();
    }

    /**
     * 从Map中安全获取Boolean值
     */
    private Boolean getBooleanValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return "1".equals(value) || "true".equalsIgnoreCase((String) value);
        }
        if (value instanceof Number) {
            return ((Number) value).intValue() == 1;
        }
        return false;
    }

    /**
     * 从Map中获取Boolean值并转换为字符串（用于标签显示）
     */
    private String getBooleanStringValue(Map<String, Object> map, String key) {
        Boolean boolValue = getBooleanValue(map, key);
        return boolValue ? "1" : "0";
    }

    /**
     * 构建区域描述
     */
    private String buildAreaDescription(List<Map<String, Object>> areas) {
        if (areas == null || areas.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < areas.size(); i++) {
            if (i > 0) {
                sb.append("、");
            }
            sb.append(areas.get(i).get("region_name"));
        }
        return sb.toString();
    }

    /**
     * 获取预警类型标签
     */
    private String getWarningTypeLabel(String warningType) {
        if (warningType == null) return "";

        // 简单的映射，实际应该从字典表查询
        switch (warningType) {
            case "1": return "台风";
            case "2": return "暴雨";
            case "3": return "暴雪";
            case "4": return "寒潮";
            case "5": return "大风";
            case "6": return "沙尘暴";
            case "7": return "高温";
            case "8": return "干旱";
            case "9": return "雷电";
            case "10": return "冰雹";
            default: return "其他";
        }
    }

    /**
     * 获取预警等级标签
     */
    private String getWarningLevelLabel(String warningLevel) {
        if (warningLevel == null) return "";

        switch (warningLevel) {
            case "5": return "蓝色预警";
            case "6": return "黄色预警";
            case "7": return "橙色预警";
            case "8": return "红色预警";
            default: return "未知等级";
        }
    }

    /**
     * 获取状态标签
     */
    private String getStatusLabel(String status) {
        if (status == null) return "";

        switch (status) {
            case "0": return "有效";
            case "1": return "失效";
            case "2": return "取消";
            default: return "未知状态";
        }
    }

    /**
     * 获取是否已通知标签
     */
    private String getIsNotifiedLabel(String isNotified) {
        if (isNotified == null) return "";

        switch (isNotified) {
            case "0": return "未通知";
            case "1": return "已通知";
            default: return "未知";
        }
    }
}
