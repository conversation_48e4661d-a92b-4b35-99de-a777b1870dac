package com.tocc.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 告警信息DTO
 * 
 * <AUTHOR>
 */
public class AlarmInfoDTO extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 告警ID */
    private String alarmId;

    /** 告警标题 */
    @Excel(name = "告警标题")
    private String alarmTitle;

    /** 告警类型（字典值） */
    @Excel(name = "告警类型")
    private String alarmType;

    /** 告警子类型（字典值） */
    @Excel(name = "告警子类型")
    private String alarmSubtype;

    /** 告警子类型列表（用于多值查询） */
    private List<String> alarmSubtypes;

    /** 告警级别（字典值） */
    @Excel(name = "告警级别")
    private String alarmLevel;

    /** 告警内容 */
    @Excel(name = "告警内容")
    private String alarmContent;

    /** 关联源数据ID */
    private String sourceId;

    /** 源数据类型（字典值） */
    private String sourceType;

    /** 所属组织ID */
    private String orgId;

    /** 所属组织名称 */
    @Excel(name = "所属组织")
    private String orgName;

    /** 状态（0未处理 1已处理 2已忽略） */
    @Excel(name = "状态")
    private String status;

    /** 告警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date alarmTime;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;

    /** 处理人ID */
    private String processorId;

    /** 处理人姓名 */
    @Excel(name = "处理人")
    private String processorName;

    /** 处理结果 */
    @Excel(name = "处理结果")
    private String processResult;

    /** 行政辖区ID */
    private String administrativeAreaId;

    /** 行政辖区名称 */
    @Excel(name = "行政辖区")
    private String administrativeArea;

    /** 排除的创建人（用于特殊业务逻辑） */
    private String excludeCreateBy;

    // Getter and Setter methods
    public String getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }

    public String getAlarmTitle() {
        return alarmTitle;
    }

    public void setAlarmTitle(String alarmTitle) {
        this.alarmTitle = alarmTitle;
    }

    public String getAlarmType() {
        return alarmType;
    }

    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }

    public String getAlarmSubtype() {
        return alarmSubtype;
    }

    public void setAlarmSubtype(String alarmSubtype) {
        this.alarmSubtype = alarmSubtype;
    }

    public List<String> getAlarmSubtypes() {
        return alarmSubtypes;
    }

    public void setAlarmSubtypes(List<String> alarmSubtypes) {
        this.alarmSubtypes = alarmSubtypes;
    }

    public String getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(String alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Date getProcessTime() {
        return processTime;
    }

    public void setProcessTime(Date processTime) {
        this.processTime = processTime;
    }

    public String getProcessorId() {
        return processorId;
    }

    public void setProcessorId(String processorId) {
        this.processorId = processorId;
    }

    public String getProcessorName() {
        return processorName;
    }

    public void setProcessorName(String processorName) {
        this.processorName = processorName;
    }

    public String getProcessResult() {
        return processResult;
    }

    public void setProcessResult(String processResult) {
        this.processResult = processResult;
    }

    public String getAdministrativeAreaId() {
        return administrativeAreaId;
    }

    public void setAdministrativeAreaId(String administrativeAreaId) {
        this.administrativeAreaId = administrativeAreaId;
    }

    public String getAdministrativeArea() {
        return administrativeArea;
    }

    public void setAdministrativeArea(String administrativeArea) {
        this.administrativeArea = administrativeArea;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getExcludeCreateBy() {
        return excludeCreateBy;
    }

    public void setExcludeCreateBy(String excludeCreateBy) {
        this.excludeCreateBy = excludeCreateBy;
    }

    @Override
    public String toString() {
        return "AlarmInfoDTO{" +
                "alarmId='" + alarmId + '\'' +
                ", alarmTitle='" + alarmTitle + '\'' +
                ", alarmType='" + alarmType + '\'' +
                ", alarmSubtype='" + alarmSubtype + '\'' +
                ", alarmLevel='" + alarmLevel + '\'' +
                ", alarmContent='" + alarmContent + '\'' +
                ", sourceId='" + sourceId + '\'' +
                ", sourceType='" + sourceType + '\'' +
                ", orgId='" + orgId + '\'' +
                ", orgName='" + orgName + '\'' +
                ", status='" + status + '\'' +
                ", alarmTime=" + alarmTime +
                ", processTime=" + processTime +
                ", processorId='" + processorId + '\'' +
                ", processorName='" + processorName + '\'' +
                ", processResult='" + processResult + '\'' +
                ", administrativeArea='" + administrativeArea + '\'' +
                ", administrativeAreaId='" + administrativeAreaId + '\'' +
                '}';
    }
}
