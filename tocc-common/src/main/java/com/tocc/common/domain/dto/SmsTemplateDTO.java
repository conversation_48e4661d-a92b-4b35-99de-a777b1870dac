package com.tocc.common.domain.dto;

public class SmsTemplateDTO {

    private String mobile;// 手机号
    private String templateId;// 模板code
    private String content;// 参数内容
    private String subAppend;//扩展码，用来回复短信使用（容联通讯独有）
    private String failReason;//发送失败原因
    private String bizId;//阿里云短信回执iD
    private Integer ret;//调用短信接口，1成功，0失败
    private String signName;// 签名
    private String templateKey;// 短信模板key

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSubAppend() {
        return subAppend;
    }

    public void setSubAppend(String subAppend) {
        this.subAppend = subAppend;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Integer getRet() {
        return ret;
    }

    public void setRet(Integer ret) {
        this.ret = ret;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public String getTemplateKey() {
        return templateKey;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }

}
