package com.tocc.common.enums;

/**
 * 声网事件枚举
 */
public enum AgoraEventType {

    CHANNEL_CREATE(101, "创建频道"),

    CHANNEL_DESTROY(102, "销毁频道"),

    BROADCASTER_JOIN_CHANNEL(103, "主播加入频道"),

    BROADCASTER_LEAVE_CHANNEL(104, "主播离开频道"),

    AUDIENCE_JOIN_CHANNEL(105, "直播观众加入频道"),

    AUDIENCE_LEAVE_CHANNEL(106, "直播观众离开频道"),

    USER_JOIN_CHANNEL(107, "用户加入频道"),

    USER_LEAVE_CHANNEL(108, "用户离开频道");

    private final int code;

    private final String description;

    AgoraEventType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static AgoraEventType fromCode(int code) {
        for (AgoraEventType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

}
