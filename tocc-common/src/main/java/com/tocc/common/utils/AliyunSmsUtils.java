package com.tocc.common.utils;

import com.aliyun.dysmsapi20170525.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.google.gson.Gson;
import com.tocc.common.domain.dto.SmsTemplateDTO;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 阿里云短信发送工具类
 */

public class AliyunSmsUtils {

    private final static Logger LOGGER = LoggerFactory.getLogger(AliyunSmsUtils.class);
    public final static Map<String, String> ERROR_MAP = new HashMap<>();
    private static com.aliyun.dysmsapi20170525.Client client = null;
    public final static String ALIYUN_ACCESS_KEY = "LTAI5tPm5x9Fz8fg7HF8qQgx";
    public final static String ALIYUN_ACCESS_KEY_SECRET = "******************************";
    public static String ALIYUN_SIGN_NAME = "智慧高速云控平台";//阿里云短信签名
//    public final static String ALIYUN_ACCESS_KEY = "LTAI5tKYHLB6uyedfQ3NGajT";
//    public final static String ALIYUN_ACCESS_KEY_SECRET = "******************************";
//    public static String ALIYUN_SIGN_NAME = "广西新发展交通集团";//阿里云短信签名

    static {
        Config config = new Config().setAccessKeyId(ALIYUN_ACCESS_KEY).setAccessKeySecret(ALIYUN_ACCESS_KEY_SECRET);
        config.endpoint = "dysmsapi.aliyuncs.com";
        try {
            client = new com.aliyun.dysmsapi20170525.Client(config);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ERROR_MAP.put("isp.RAM_PERMISSION_DENY","RAM权限不足");
        ERROR_MAP.put("isv.OUT_OF_SERVICE","业务停机");
        ERROR_MAP.put("isv.PRODUCT_UN_SUBSCRIPT","未开通云通信产品的阿里云客户");
        ERROR_MAP.put("isv.PRODUCT_UNSUBSCRIBE","产品未开通");
        ERROR_MAP.put("isv.ACCOUNT_NOT_EXISTS","账户不存在");
        ERROR_MAP.put("isv.ACCOUNT_ABNORMAL","账户异常");
        ERROR_MAP.put("isv.SMS_TEMPLATE_ILLEGAL","该账号下找不到对应模板");
        ERROR_MAP.put("isv.SMS_TEST_SIGN_TEMPLATE_LIMIT","测试模板和签名限制");
        ERROR_MAP.put("isv.SMS_SIGNATURE_SCENE_ILLEGAL","签名和模板类型不一致");
        ERROR_MAP.put("isv.SMS_SIGN_ILLEGAL","签名禁止使用");
        ERROR_MAP.put("isv.SMS_SIGNATURE_ILLEGAL","该账号下找不到对应签名");
        ERROR_MAP.put("isp.SYSTEM_ERROR","系统出现错误，请重新调用");
        ERROR_MAP.put("isv.MOBILE_NUMBER_ILLEGAL","手机号码格式错误");
        ERROR_MAP.put("isv.MOBILE_COUNT_OVER_LIMIT","手机号码数量超过限制，最多支持1000条");
        ERROR_MAP.put("isv.TEMPLATE_MISSING_PARAMETERS","模板变量中存在未赋值变量");
        ERROR_MAP.put("isv.TEMPLATE_PARAMS_ILLEGAL","传入的变量内容和实际申请模板时变量所选择的属性类型不配");
        ERROR_MAP.put("isv.TEMPLATE_COUNT_OVER_LIMIT","超过单自然日模板申请数量上限");
        ERROR_MAP.put("isv.TEMPLATE_OVER_LIMIT","模板字符数量超过限制");
        ERROR_MAP.put("isv.BUSINESS_LIMIT_CONTROL","触发云通信流控限制");
        ERROR_MAP.put("isv.INVALID_JSON_PARAM","参数格式错误，请修改为字符串值");
        ERROR_MAP.put("isv.INVALID_PARAMETERS","参数格式不正确");
        ERROR_MAP.put("isv.BLACK_KEY_CONTROL_LIMIT","变量中传入疑似违规信息");
        ERROR_MAP.put("isv.PARAM_LENGTH_LIMIT","参数超过长度限制");
        ERROR_MAP.put("isv.PARAM_NOT_SUPPORT_URL","变量不支持传入URL");
        ERROR_MAP.put("isv.AMOUNT_NOT_ENOUGH","账户余额不足");
        ERROR_MAP.put("FILTER","关键字拦截");
        ERROR_MAP.put("VALVE:M_MC","重复过滤");
        ERROR_MAP.put("VALVE:H_MC","重复过滤");
        ERROR_MAP.put("VALVE:D_MC","重复过滤");
        ERROR_MAP.put("MOBILE_SEND_LIMIT","单个号码日或月发送上限，流控超限，频繁发送超限");
        ERROR_MAP.put("isv.TEMPLATE_PARAMS_ILLEGAL","传入的变量内容和实际申请模板时变量所选择的属性类型不配");
        ERROR_MAP.put("MOBILE_IN_BLACK","手机号在黑名单（平台或运营商）");
        ERROR_MAP.put("MOBILE_TERMINAL_ERROR","手机终端问题、内存满、SIM卡满、非法设备等");
        ERROR_MAP.put("MOBILE_NOT_ON_SERVICE","停机、空号、暂停服务、关机、不在服务区");
        ERROR_MAP.put("SP_NOT_BY_INTER_SMS","未开通国际短信");
        ERROR_MAP.put("USER_REJECT","用户手机退订此业务、产品未开通");
        ERROR_MAP.put("SP_UNKNOWN_ERROR","运营商未知错误");
        ERROR_MAP.put("MOBILE_ACCOUNT_ABNORMAL","用户账户异常、携号转网、欠费等");
        ERROR_MAP.put("DELIVERED","消息发送成功");
        ERROR_MAP.put("REQUEST_SUCCESS","请求成功");
        ERROR_MAP.put("CONTENT_KEYWORD","内容关键字拦截");
        ERROR_MAP.put("SIGNATURE_BLACKLIST","签名黑名单");
        ERROR_MAP.put("INVALID_NUMBER","号码状态异常");
        ERROR_MAP.put("NO_ROUTE","无路由器");
        ERROR_MAP.put("CONTENT_ERROR","模板内容无退订");

    }

    public static void init(String accessKey, String accessSecret, String signName) {
        ALIYUN_SIGN_NAME = signName;
        Config config = new Config().setAccessKeyId(accessKey).setAccessKeySecret(accessSecret);
        config.endpoint = "dysmsapi.aliyuncs.com";
        try {
            client = new com.aliyun.dysmsapi20170525.Client(config);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @描述 单条短信发送
     * @return
     */
    public static boolean sendAnSms(SmsTemplateDTO smsTemplateDTO) {
        String mobile = smsTemplateDTO.getMobile();
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(mobile)
                .setSignName(ALIYUN_SIGN_NAME)
                .setTemplateCode(smsTemplateDTO.getTemplateId())
                //"{\"time\":\"2022年06月02 12:00:15\",\"address\":\"S50清凭高速(吴大路)K120+152\",\"content\":\"团福互通往那齐互通(上行)，车辆打不着火，停在应急车道，需要先咨询拖车费用，已转接沿海分中心电话13737770000，考虑存在安全隐患，建立工单派发。（请确认来电人是否已拨打12122报警，如未报警，请联系交警、路政等相关部门做好报备工作） 死亡 0 人，失踪 0 人，受伤 0 人。\"}"
                .setTemplateParam(smsTemplateDTO.getContent().replace("\\", "\\\\"));
        RuntimeOptions runtime = new RuntimeOptions();

        SendSmsResponse sendSmsResponse = null;
        try {
            sendSmsResponse = client.sendSmsWithOptions(sendSmsRequest, runtime);
            SendSmsResponseBody body = sendSmsResponse.getBody();
            String bizId = body.getBizId();
            String message = body.getMessage();
            LOGGER.info("bizId:{},message:{},code:{}", bizId, message, body.getCode());
            smsTemplateDTO.setBizId(bizId);
            if("OK".equalsIgnoreCase(message)) {
                smsTemplateDTO.setRet(1);
                return true;
            } else {
                smsTemplateDTO.setRet(0);
                smsTemplateDTO.setFailReason(message);
                LOGGER.error("短信发送失败:{}", message);
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("调用阿里云短信接口sendSmsWithOptions失败:{}", e.getMessage());
        }
        return false;
    }

    /**
     * @描述 单条短信发送详情
     * @return
     */
    public static String querySendDetails(String mobile, String date, String bizId) {
        QuerySendDetailsRequest request = new QuerySendDetailsRequest();
        request.setPhoneNumber(mobile);
        request.setSendDate(date);
        request.setPageSize(1L);
        request.setCurrentPage(1L);
        request.setBizId(bizId);
        try {
            QuerySendDetailsResponse querySendDetails = client.querySendDetails(request);
            System.out.println(new Gson().toJson(querySendDetails));
            QuerySendDetailsResponseBody body = querySendDetails.getBody();
            String totalCount = body.getTotalCount();
            if(NumberUtils.toInt(totalCount) == 1) {
                QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOs smsSendDetailDTOs = body.getSmsSendDetailDTOs();
                List<QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO> smsSendDetailDTOList = smsSendDetailDTOs.getSmsSendDetailDTO();
                if(!CollectionUtils.isEmpty(smsSendDetailDTOList)) {
                    QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO smsSendDetailDTO = smsSendDetailDTOList.get(0);
                    Long sendStatus = smsSendDetailDTO.getSendStatus();
                    if(sendStatus == 2) {//失败
                        String errCode = smsSendDetailDTO.getErrCode();
                        return errCode;
                    } else if(sendStatus == 3) {//成功
                        return "OK";
                    }
                }

            }
        } catch (ServerException e) {
            e.printStackTrace();
        } catch (ClientException e) {
            System.out.println("ErrCode:" + e.getErrCode());
            System.out.println("ErrMsg:" + e.getErrMsg());
            System.out.println("RequestId:" + e.getRequestId());
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("调用阿里云短信接口querySendDetails失败:{}", e.getMessage());
        }
        return "ITSWAY_ERROR";
    }

    /**

     IAcsClient client = new DefaultAcsClient(profile);


     QuerySendDetailsRequest request = new QuerySendDetailsRequest();

     try {
     QuerySendDetailsResponse response = client.getAcsResponse(request);
     System.out.println(new Gson().toJson(response));
     } catch (ServerException e) {
     e.printStackTrace();
     } catch (ClientException e) {
     System.out.println("ErrCode:" + e.getErrCode());
     System.out.println("ErrMsg:" + e.getErrMsg());
     System.out.println("RequestId:" + e.getRequestId());
     }
     https://next.api.aliyun.com/api/Dysmsapi/2017-05-25/QuerySendDetails?sdkStyle=old

     https://next.api.aliyun.com/api/Dysmsapi/2017-05-25/QuerySendDetails?sdkStyle=old&accounttraceid=7c781fe6155a4927b77245f6f4347e3condr&lang=JAVA&tab=DOC
     短信发送状态，包括：
     SendStatus
     1：等待回执。
     2：发送失败。
     3：发送成功。

     */

}
