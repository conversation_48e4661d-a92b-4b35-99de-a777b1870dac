package com.tocc.system.service.impl;

import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.UUID;
import com.tocc.system.domain.dto.ExpertInfoDTO;
import com.tocc.system.domain.vo.ExpertInfoVO;
import com.tocc.system.mapper.ExpertInfoMapper;
import com.tocc.system.service.IExpertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 专家信息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ExpertServiceImpl implements IExpertService {


    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    /**
     * 新增专家信息
     *
     * @param expertInfo 专家信息
     * @return 结果
     */
    @Override
    public int insertExpertInfo(ExpertInfoDTO expertInfo) {
        expertInfo.setId(UUID.randomUUID().toString());
        Date now = new Date();
        expertInfo.setCreateTime(now);
        expertInfo.setUpdateTime(now);
        expertInfo.setCreateBy(SecurityUtils.getUsername());
        expertInfo.setUpdateBy(SecurityUtils.getUsername());
        return expertInfoMapper.insertExpertInfo(expertInfo);
    }

    /**
     * 修改专家信息
     *
     * @param expertInfo 专家信息
     * @return 结果
     */
    @Override
    public int updateExpertInfo(ExpertInfoDTO expertInfo) {
        int count = expertInfoMapper.countById(expertInfo.getId());
        if(count == 0){
            throw new  ServiceException("专家不存在，修改失败");
        }
        expertInfo.setUpdateTime(new Date());
        expertInfo.setUpdateBy(SecurityUtils.getUsername());
        return expertInfoMapper.updateExpertInfo(expertInfo);
    }

    /**
     * 根据用户ID删除专家信息
     *
     * @param id 用户ID
     * @return 结果
     */
    @Override
    public int deleteExpertInfoById(String id) {
        return expertInfoMapper.deleteExpertInfoById(id);
    }

    /**
     * 根据用户ID查询专家信息
     *
     * @param id 主键
     * @return 专家信息
     */
    @Override
    public ExpertInfoVO selectExpertInfoById(String id) {
        return expertInfoMapper.selectExpertInfoById(id);
    }

    /**
     * 查询专家信息列表
     *
     * @param expertInfo 专家信息查询条件
     * @return 专家信息列表
     */
    @Override
    public List<ExpertInfoVO> selectExpertInfoList(ExpertInfoDTO expertInfo) {
        return expertInfoMapper.selectExpertInfoList(expertInfo);
    }

    /**
     * 查询超时的专家信息
     *
     * @param timeoutTime 超时时间点
     * @return 超时的专家列表
     */
    @Override
    public List<ExpertInfoVO> selectTimeoutExperts(Date timeoutTime) {
        return expertInfoMapper.selectTimeoutExperts(timeoutTime);
    }

    /**
     * 批量删除专家信息
     *
     * @param idList 用户ID数组
     * @return 结果
     */
    @Override
    public int deleteExpertInfoByIds(List<String> idList) {
        return expertInfoMapper.deleteExpertInfoByIds(idList);
    }

    /**
     * 更新专家最后更新时间
     *
     * @param id 主键
     * @return 结果
     */
    @Override
    public int updateLastUpdateTime(String id) {
        ExpertInfoDTO expertInfo = new ExpertInfoDTO();
        expertInfo.setId(id);
        expertInfo.setUpdateTime(new Date());
        return expertInfoMapper.updateExpertInfo(expertInfo);
    }
}
