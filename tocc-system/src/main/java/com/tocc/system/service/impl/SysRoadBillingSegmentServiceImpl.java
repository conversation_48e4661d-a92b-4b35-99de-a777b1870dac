package com.tocc.system.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSON;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.system.domain.vo.RoadSectionGeometryVO;
import com.tocc.system.domain.vo.BillingSegmentVO;
import com.tocc.system.config.CoordinateSimplifyConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.system.mapper.SysRoadBillingSegmentMapper;
import com.tocc.system.domain.SysRoadBillingSegment;
import com.tocc.system.service.ISysRoadBillingSegmentService;

/**
 * 高速公路计费路段信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Service
public class SysRoadBillingSegmentServiceImpl implements ISysRoadBillingSegmentService 
{
    private static final Logger log = LoggerFactory.getLogger(SysRoadBillingSegmentServiceImpl.class);

    @Autowired
    private SysRoadBillingSegmentMapper sysRoadBillingSegmentMapper;

    @Autowired
    private CoordinateSimplifyConfig simplifyConfig;

    /**
     * 查询高速公路计费路段信息
     * 
     * @param id 高速公路计费路段信息主键
     * @return 高速公路计费路段信息
     */
    @Override
    public SysRoadBillingSegment selectSysRoadBillingSegmentById(Long id)
    {
        return sysRoadBillingSegmentMapper.selectSysRoadBillingSegmentById(id);
    }

    /**
     * 查询高速公路计费路段信息列表
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 高速公路计费路段信息
     */
    @Override
    public List<SysRoadBillingSegment> selectSysRoadBillingSegmentList(SysRoadBillingSegment sysRoadBillingSegment)
    {
        return sysRoadBillingSegmentMapper.selectSysRoadBillingSegmentList(sysRoadBillingSegment);
    }

    /**
     * 新增高速公路计费路段信息
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 结果
     */
    @Override
    public int insertSysRoadBillingSegment(SysRoadBillingSegment sysRoadBillingSegment)
    {
        sysRoadBillingSegment.setCreateTime(DateUtils.getNowDate());
        return sysRoadBillingSegmentMapper.insertSysRoadBillingSegment(sysRoadBillingSegment);
    }

    /**
     * 修改高速公路计费路段信息
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 结果
     */
    @Override
    public int updateSysRoadBillingSegment(SysRoadBillingSegment sysRoadBillingSegment)
    {
        sysRoadBillingSegment.setUpdateTime(DateUtils.getNowDate());
        return sysRoadBillingSegmentMapper.updateSysRoadBillingSegment(sysRoadBillingSegment);
    }

    /**
     * 批量删除高速公路计费路段信息
     * 
     * @param ids 需要删除的高速公路计费路段信息主键
     * @return 结果
     */
    @Override
    public int deleteSysRoadBillingSegmentByIds(Long[] ids)
    {
        return sysRoadBillingSegmentMapper.deleteSysRoadBillingSegmentByIds(ids);
    }

    /**
     * 删除高速公路计费路段信息信息
     * 
     * @param id 高速公路计费路段信息主键
     * @return 结果
     */
    @Override
    public int deleteSysRoadBillingSegmentById(Long id)
    {
        return sysRoadBillingSegmentMapper.deleteSysRoadBillingSegmentById(id);
    }

    /**
     * 根据公路编码和路段名称获取路段几何信息
     *
     * @param code 公路编码
     * @param sectionName 路段名称
     * @return 路段几何信息
     */
    @Override
    public RoadSectionGeometryVO getRoadSectionGeometry(String code, String sectionName) {
        return getRoadSectionGeometry(code, sectionName, true); // 默认启用抽稀
    }

    /**
     * 根据公路编码和路段名称获取路段几何信息
     *
     * @param code 公路编码
     * @param sectionName 路段名称
     * @param simplify 是否启用坐标抽稀
     * @return 路段几何信息
     */
    @Override
    public RoadSectionGeometryVO getRoadSectionGeometry(String code, String sectionName, Boolean simplify) {
        // 1. 查询该路段下所有计费路段
        List<Map<String, Object>> segments = sysRoadBillingSegmentMapper
            .selectGeometryByCodeAndSection(code, sectionName);
        
        if (segments.isEmpty()) {
            throw new ServiceException("未找到指定路段的几何信息");
        }
        
        // 2. 构建返回结果
        RoadSectionGeometryVO result = new RoadSectionGeometryVO();
        result.setRoadCode(code);
        result.setSectionName(sectionName);
        
        // 3. 计算总长度和路段数量
        BigDecimal totalLength = segments.stream()
            .filter(s -> s.get("length_road") != null)
            .map(s -> (BigDecimal) s.get("length_road"))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setTotalLength(totalLength);
        result.setSegmentCount(segments.size());
        
        // 4. 构建计费路段列表
        List<BillingSegmentVO> billingSegments = buildBillingSegmentList(segments, simplify);
        result.setBillingSegments(billingSegments);
        
        return result;
    }

    /**
     * 根据road_marker_id获取路段几何信息
     *
     * @param roadMarkerId 公路编号ID
     * @return 路段几何信息
     */
    @Override
    public RoadSectionGeometryVO getRoadSectionGeometryById(Long roadMarkerId) {
        return getRoadSectionGeometryById(roadMarkerId, true); // 默认启用抽稀
    }

    /**
     * 根据road_marker_id获取路段几何信息
     *
     * @param roadMarkerId 公路编号ID
     * @param simplify 是否启用坐标抽稀
     * @return 路段几何信息
     */
    @Override
    public RoadSectionGeometryVO getRoadSectionGeometryById(Long roadMarkerId, Boolean simplify) {
        // 1. 查询该路段下所有计费路段
        List<Map<String, Object>> segments = sysRoadBillingSegmentMapper
            .selectGeometryByRoadMarkerId(roadMarkerId);
        
        if (segments.isEmpty()) {
            throw new ServiceException("未找到指定路段的几何信息");
        }
        
        // 2. 构建返回结果
        RoadSectionGeometryVO result = new RoadSectionGeometryVO();
        Map<String, Object> firstSegment = segments.get(0);
        result.setRoadCode((String) firstSegment.get("road_code"));
        result.setSectionName((String) firstSegment.get("section_name"));
        
        // 3. 计算总长度和路段数量
        BigDecimal totalLength = segments.stream()
            .filter(s -> s.get("length_road") != null)
            .map(s -> (BigDecimal) s.get("length_road"))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setTotalLength(totalLength);
        result.setSegmentCount(segments.size());
        
        // 4. 构建计费路段列表
        List<BillingSegmentVO> billingSegments = buildBillingSegmentList(segments, simplify);
        result.setBillingSegments(billingSegments);
        
        return result;
    }

    /**
     * 根据road_marker_id查询计费路段列表
     *
     * @param roadMarkerId 公路编号ID
     * @return 计费路段集合
     */
    @Override
    public List<SysRoadBillingSegment> selectByRoadMarkerId(Long roadMarkerId) {
        return sysRoadBillingSegmentMapper.selectByRoadMarkerId(roadMarkerId);
    }

    /**
     * 获取所有路段的几何信息
     *
     * @return 所有路段几何信息集合
     */
    @Override
    public List<RoadSectionGeometryVO> getAllRoadSectionGeometry() {
        return getAllRoadSectionGeometry(true); // 默认启用抽稀
    }

    /**
     * 获取所有路段的几何信息
     *
     * @param simplify 是否启用坐标抽稀
     * @return 所有路段几何信息集合
     */
    @Override
    public List<RoadSectionGeometryVO> getAllRoadSectionGeometry(Boolean simplify) {
        // 1. 查询所有路段的几何信息
        List<Map<String, Object>> allSegments = sysRoadBillingSegmentMapper.selectAllRoadSectionGeometry();

        if (allSegments.isEmpty()) {
            log.warn("未找到任何路段几何信息");
            return new ArrayList<>();
        }

//        log.info("查询到 {} 个计费路段，开始按路段分组", allSegments.size());

        // 2. 按路段分组（code + sectionName）
        Map<String, List<Map<String, Object>>> groupedSegments = new HashMap<>();

        for (Map<String, Object> segment : allSegments) {
            String roadCode = (String) segment.get("road_code");
            String sectionName = (String) segment.get("section_name");

            if (roadCode != null && sectionName != null) {
                String key = roadCode + "-" + sectionName;
                groupedSegments.computeIfAbsent(key, k -> new ArrayList<>()).add(segment);
            }
        }

//        log.info("分组完成，共 {} 个路段", groupedSegments.size());

        // 3. 为每个路段构建几何信息
        List<RoadSectionGeometryVO> results = new ArrayList<>();

        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedSegments.entrySet()) {
            String key = entry.getKey();
            List<Map<String, Object>> segments = entry.getValue();

            try {
                // 从第一个路段获取基本信息
                Map<String, Object> firstSegment = segments.get(0);
                String roadCode = (String) firstSegment.get("road_code");
                String sectionName = (String) firstSegment.get("section_name");

                RoadSectionGeometryVO result = new RoadSectionGeometryVO();
                result.setRoadCode(roadCode);
                result.setSectionName(sectionName);

                // 计算总长度和路段数量
                BigDecimal totalLength = segments.stream()
                    .filter(s -> s.get("length_road") != null)
                    .map(s -> (BigDecimal) s.get("length_road"))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setTotalLength(totalLength);
                result.setSegmentCount(segments.size());

                // 构建计费路段列表
                List<BillingSegmentVO> billingSegments = buildBillingSegmentList(segments, simplify);
                result.setBillingSegments(billingSegments);

                results.add(result);

//                log.debug("路段 {} 处理完成，包含 {} 个计费路段",
//                    key, segments.size());

            } catch (Exception e) {
                log.warn("处理路段 {} 时发生异常: {}", key, e.getMessage(), e);
            }
        }

//        log.info("所有路段几何信息处理完成，返回 {} 个路段", results.size());
        return results;
    }

    /**
     * 构建计费路段列表
     */
    private List<BillingSegmentVO> buildBillingSegmentList(List<Map<String, Object>> segments, Boolean simplify) {
        List<BillingSegmentVO> billingSegments = new ArrayList<>();

        if (segments == null || segments.isEmpty()) {
//            log.warn("路段数据为空");
            return billingSegments;
        }

//        log.info("开始构建 {} 个计费路段", segments.size());

        // 按桩号排序，确保路段顺序正确
        try {
            segments.sort((a, b) -> {
                try {
                    String km1A = (String) a.get("km1");
                    String km1B = (String) b.get("km1");
                    String directionA = (String) a.get("direction");
                    String directionB = (String) b.get("direction");

                    // 先按方向排序（上行优先），再按桩号排序
                    if (directionA != null && directionB != null && !directionA.equals(directionB)) {
                        return "上行".equals(directionA) ? -1 : 1;
                    }

                    // 简单的桩号比较（实际项目中可能需要更复杂的桩号解析）
                    if (km1A != null && km1B != null) {
                        return km1A.compareTo(km1B);
                    }
                    return 0;
                } catch (Exception e) {
                    log.warn("排序时出现异常: {}", e.getMessage());
                    return 0;
                }
            });
        } catch (Exception e) {
            log.warn("路段排序失败，使用原始顺序: {}", e.getMessage());
        }

        for (Map<String, Object> segment : segments) {
            try {
                BillingSegmentVO billingSegment = new BillingSegmentVO();

                // 设置基本信息
                billingSegment.setId(getLongValue(segment.get("id")));
                billingSegment.setName((String) segment.get("segment_name"));
                billingSegment.setCode((String) segment.get("code"));
                billingSegment.setDirection((String) segment.get("direction"));
                billingSegment.setLength((BigDecimal) segment.get("length_road"));
                billingSegment.setKmStart((String) segment.get("km1"));
                billingSegment.setKmEnd((String) segment.get("km2"));
                billingSegment.setCompany((String) segment.get("company"));
                billingSegment.setPlanSpeed(getIntegerValue(segment.get("plan_speed")));
                billingSegment.setCarWayNum((String) segment.get("car_way_num"));

                // 解析几何信息
                String geomStr = (String) segment.get("geom");
                if (StringUtils.isNotEmpty(geomStr)) {
                    List<List<Double>> coordinates = parseGeometryCoordinates(geomStr);

                    // 根据参数决定是否进行抽稀处理
                    if (simplify != null && simplify) {
                        List<List<Double>> simplifiedCoordinates = simplifyCoordinates(coordinates);
                        billingSegment.setCoordinates(simplifiedCoordinates);

//                        log.debug("计费路段 {} 原始坐标点: {}, 抽稀后: {}",
//                            billingSegment.getId(), coordinates.size(), simplifiedCoordinates.size());
                    } else {
                        billingSegment.setCoordinates(coordinates);

//                        log.debug("计费路段 {} 坐标点: {} (未抽稀)",
//                            billingSegment.getId(), coordinates.size());
                    }
                } else {
                    log.warn("计费路段 {} 的几何信息为空", billingSegment.getId());
                    billingSegment.setCoordinates(new ArrayList<>());
                }

                billingSegments.add(billingSegment);

            } catch (Exception e) {
                log.warn("构建计费路段失败，路段ID: {}", segment.get("id"), e);
            }
        }

//        log.info("计费路段构建完成，共 {} 个计费路段", billingSegments.size());
        return billingSegments;
    }

    /**
     * 解析几何坐标信息
     */
    private List<List<Double>> parseGeometryCoordinates(String geomStr) {
        List<List<Double>> coordinates = new ArrayList<>();

        try {
            // 解析GeoJSON几何信息
            Map<String, Object> geometry = JSON.parseObject(geomStr, Map.class);

            if ("LineString".equals(geometry.get("type"))) {
                @SuppressWarnings("unchecked")
                List<List<Object>> rawCoordinates = (List<List<Object>>) geometry.get("coordinates");

                if (rawCoordinates != null && !rawCoordinates.isEmpty()) {
                    for (List<Object> rawCoordinate : rawCoordinates) {
                        List<Double> coordinate = convertToDoubleList(rawCoordinate);
                        coordinates.add(coordinate);
                    }
                }
            } else {
                log.warn("几何类型不是LineString: {}", geometry.get("type"));
            }
        } catch (Exception e) {
            log.warn("解析几何信息失败: {}", geomStr, e);
        }

        return coordinates;
    }

    /**
     * 获取Long值
     */
    private Long getLongValue(Object value) {
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Number) return ((Number) value).longValue();
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取Integer值
     */
    private Integer getIntegerValue(Object value) {
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Number) return ((Number) value).intValue();
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 转换坐标列表类型（处理BigDecimal等类型转换为Double）
     */
    private List<Double> convertToDoubleList(List<Object> rawCoordinate) {
        List<Double> coordinate = new ArrayList<>();

        if (rawCoordinate == null || rawCoordinate.isEmpty()) {
            log.warn("原始坐标数据为空");
            return coordinate;
        }

        for (int i = 0; i < rawCoordinate.size(); i++) {
            Object value = rawCoordinate.get(i);
            try {
                if (value instanceof Number) {
                    double doubleValue = ((Number) value).doubleValue();
                    coordinate.add(doubleValue);
                } else if (value != null) {
                    // 如果不是数字类型，尝试解析字符串
                    double doubleValue = Double.parseDouble(value.toString());
                    coordinate.add(doubleValue);
                } else {
                    log.warn("坐标值[{}]为null，使用默认值0.0", i);
                    coordinate.add(0.0);
                }
            } catch (NumberFormatException e) {
                log.warn("无法解析坐标值[{}]: {}, 使用默认值0.0", i, value);
                coordinate.add(0.0); // 默认值
            } catch (Exception e) {
                log.warn("转换坐标值[{}]时发生异常: {}, 使用默认值0.0", i, value, e);
                coordinate.add(0.0);
            }
        }

        return coordinate;
    }

    /**
     * 坐标抽稀算法 - 保留首尾点，中间按间隔抽取
     */
    private List<List<Double>> simplifyCoordinates(List<List<Double>> coordinates) {
        if (coordinates == null || coordinates.size() <= 2) {
            return coordinates; // 少于等于2个点，不需要抽稀
        }

        List<List<Double>> simplified = new ArrayList<>();
        int totalPoints = coordinates.size();

        // 根据点数量动态调整抽稀间隔
        int interval = calculateInterval(totalPoints);

        // 始终保留第一个点
        simplified.add(coordinates.get(0));

        // 中间点按间隔抽取
        for (int i = interval; i < totalPoints - 1; i += interval) {
            simplified.add(coordinates.get(i));
        }

        // 始终保留最后一个点（避免重复添加）
        if (totalPoints > 1 && !isSameCoordinate(simplified.get(simplified.size() - 1), coordinates.get(totalPoints - 1))) {
            simplified.add(coordinates.get(totalPoints - 1));
        }

        return simplified;
    }

    /**
     * 根据总点数计算抽稀间隔
     */
    private int calculateInterval(int totalPoints) {
        CoordinateSimplifyConfig.IntervalConfig interval = simplifyConfig.getInterval();

        if (totalPoints <= 10) return interval.getSmall();
        if (totalPoints <= 50) return interval.getMedium();
        if (totalPoints <= 100) return interval.getLarge();
        if (totalPoints <= 200) return interval.getXlarge();
        if (totalPoints <= 500) return interval.getXxlarge();
        if (totalPoints <= 1000) return interval.getHuge();
        return interval.getMassive();
    }

    /**
     * 道格拉斯-普克算法抽稀（可选的高级抽稀算法）
     */
    private List<List<Double>> douglasPeucker(List<List<Double>> coordinates, double tolerance) {
        if (coordinates.size() <= 2) {
            return coordinates;
        }

        // 找到距离首尾连线最远的点
        double maxDistance = 0;
        int maxIndex = 0;
        List<Double> start = coordinates.get(0);
        List<Double> end = coordinates.get(coordinates.size() - 1);

        for (int i = 1; i < coordinates.size() - 1; i++) {
            double distance = pointToLineDistance(coordinates.get(i), start, end);
            if (distance > maxDistance) {
                maxDistance = distance;
                maxIndex = i;
            }
        }

        List<List<Double>> result = new ArrayList<>();

        if (maxDistance > tolerance) {
            // 递归处理两段
            List<List<Double>> left = douglasPeucker(coordinates.subList(0, maxIndex + 1), tolerance);
            List<List<Double>> right = douglasPeucker(coordinates.subList(maxIndex, coordinates.size()), tolerance);

            result.addAll(left.subList(0, left.size() - 1)); // 去掉重复的中间点
            result.addAll(right);
        } else {
            // 距离小于容差，只保留首尾点
            result.add(start);
            result.add(end);
        }

        return result;
    }

    /**
     * 计算点到直线的距离
     */
    private double pointToLineDistance(List<Double> point, List<Double> lineStart, List<Double> lineEnd) {
        double x0 = point.get(0);
        double y0 = point.get(1);
        double x1 = lineStart.get(0);
        double y1 = lineStart.get(1);
        double x2 = lineEnd.get(0);
        double y2 = lineEnd.get(1);

        double A = y2 - y1;
        double B = x1 - x2;
        double C = x2 * y1 - x1 * y2;

        return Math.abs(A * x0 + B * y0 + C) / Math.sqrt(A * A + B * B);
    }

    /**
     * 判断两个坐标点是否相同（精度容差）
     */
    private boolean isSameCoordinate(List<Double> coord1, List<Double> coord2) {
        if (coord1.size() != coord2.size()) {
            return false;
        }

        double tolerance = 0.000001; // 坐标精度容差
        for (int i = 0; i < coord1.size(); i++) {
            if (Math.abs(coord1.get(i) - coord2.get(i)) > tolerance) {
                return false;
            }
        }
        return true;
    }
}
