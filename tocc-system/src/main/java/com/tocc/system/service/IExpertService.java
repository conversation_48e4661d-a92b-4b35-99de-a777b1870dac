package com.tocc.system.service;

import com.tocc.system.domain.dto.ExpertInfoDTO;
import com.tocc.system.domain.vo.ExpertInfoVO;

import java.util.Date;
import java.util.List;

/**
 * 专家信息Service接口
 *
 * <AUTHOR>
 */
public interface IExpertService {

    /**
     * 新增专家信息
     *
     * @param expertInfo 专家信息
     * @return 结果
     */
    int insertExpertInfo(ExpertInfoDTO expertInfo);

    /**
     * 修改专家信息
     *
     * @param expertInfo 专家信息
     * @return 结果
     */
    int updateExpertInfo(ExpertInfoDTO expertInfo);

    /**
     * 根据用户ID删除专家信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteExpertInfoById(String userId);

    /**
     * 根据用户ID查询专家信息
     *
     * @param id 主键
     * @return 专家信息
     */
    ExpertInfoVO selectExpertInfoById(String id);

    /**
     * 查询专家信息列表
     *
     * @param expertInfo 专家信息查询条件
     * @return 专家信息列表
     */
    List<ExpertInfoVO> selectExpertInfoList(ExpertInfoDTO expertInfo);

    /**
     * 查询超时的专家信息
     *
     * @param timeoutTime 超时时间点
     * @return 超时的专家列表
     */
    List<ExpertInfoVO> selectTimeoutExperts(Date timeoutTime);

    /**
     * 批量删除专家信息
     *
     * @param idList 用户ID数组
     * @return 结果
     */
    int deleteExpertInfoByIds(List<String> idList);

    /**
     * 更新专家最后更新时间
     *
     * @param id 主键
     * @return 结果
     */
    int updateLastUpdateTime(String id);
}
