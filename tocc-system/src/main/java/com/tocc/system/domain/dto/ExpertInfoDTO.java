package com.tocc.system.domain.dto;

import com.tocc.common.core.domain.BaseEntity;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 专家信息DTO
 *
 * <AUTHOR>
 */
@Data
public class ExpertInfoDTO extends BaseEntity {

    /**
     * 主键
     */
    private String id;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @Length(max = 100, message = "姓名不能超过100个字符")
    private String name;

    /**
     * 性别
     * （0男 1女 2未知）
     */
    @NotBlank(message = "性别不能为空")
    @Length(max = 1, message = "性别不能超过1个字符")
    private String sex;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 部门Id
     */
    private Long deptId;

    /**
     * 从事专业（擅长专业）
     */
    @NotBlank(message = "从事专业（擅长专业）")
    private String specialtyField;

    /**
     * 职称
     */
    private String professionalTitle;

    /**
     * 职务
     */
    private String post;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 学历
     */
    @NotBlank(message = "学历不能为空")
    private String education;

    /**
     * 学历专业
     */
    @NotBlank(message = "专业不能为空")
    private String major;

    /**
     * 毕业学校
     */
    @NotBlank(message = "毕业学校不能为空")
    private String graduationSchool;

    /**
     * 联系电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    private String phone;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式错误")
    private String email;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 申报类型
     */
    private String declarationType;
}
