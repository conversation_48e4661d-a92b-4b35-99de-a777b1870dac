package com.tocc.risk.service.impl;

import java.util.*;

import cn.hutool.core.util.IdUtil;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.RiskProjectsMapper;
import com.tocc.risk.domain.Projects;
import com.tocc.risk.service.IRiskProjectsService;

/**
 * 项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class RiskProjectsServiceImpl implements IRiskProjectsService 
{
    @Autowired
    private RiskProjectsMapper riskProjectsMapper;

    /**
     * 查询项目
     * 
     * @param id 项目主键
     * @return 项目
     */
    @Override
    public Projects selectRiskProjectsById(String id)
    {
        return riskProjectsMapper.selectRiskProjectsById(id);
    }

    /**
     * 查询项目列表
     * 
     * @param projects 项目
     * @return 项目
     */
    @Override
    public List<Projects> selectRiskProjectsList(Projects projects)
    {
        return riskProjectsMapper.selectRiskProjectsList(projects);
    }

    @Override
    public List<Map> getProjectCharger(Projects projects) {
        List<Projects> list = riskProjectsMapper.selectRiskProjectsList(projects);
        List<Map> mapList = new ArrayList<>();
        for (Projects pro : list) {
            // 没有建设单位包保责任人的不需要列出，因为下发不了任务
            if (pro.getBuilderId() != null) {
                Map map = new HashMap<>();
                map.put("id", pro.getId());
                map.put("name", pro.getProjectName() + pro.getWhistling());
                // 项目人员
                Map m = new HashMap<>();
                m.put("id", pro.getBuilderId());
                m.put("name", pro.getBuilder());
                m.put("tel", pro.getBuilderTel());
                m.put("projectId", pro.getId());
                List<Map> child = new ArrayList<>();
                child.add(m);
                map.put("children", child);
                mapList.add(map);
            }
        }
        return mapList;
    }

    /**
     * 新增项目
     * 
     * @param projects 项目
     * @return 结果
     */
    @Override
    public int insertRiskProjects(Projects projects)
    {
        projects.setId(IdUtil.fastSimpleUUID());
        projects.setDelFlag(0);
        projects.setCreateTime(DateUtils.getNowDate());
        return riskProjectsMapper.insertRiskProjects(projects);
    }

    /**
     * 修改项目
     * 
     * @param projects 项目
     * @return 结果
     */
    @Override
    public int updateRiskProjects(Projects projects)
    {
        projects.setUpdateTime(DateUtils.getNowDate());
        return riskProjectsMapper.updateRiskProjects(projects);
    }

    /**
     * 批量删除项目
     * 
     * @param ids 需要删除的项目主键
     * @return 结果
     */
    @Override
    public int deleteRiskProjectsByIds(Long[] ids)
    {
        return riskProjectsMapper.deleteRiskProjectsByIds(ids);
    }

    /**
     * 删除项目信息
     * 
     * @param id 项目主键
     * @return 结果
     */
    @Override
    public int deleteRiskProjectsById(String id)
    {
        return riskProjectsMapper.deleteRiskProjectsById(id);
    }
}
