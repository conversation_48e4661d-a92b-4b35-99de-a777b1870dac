package com.tocc.risk.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.IdUtil;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.risk.domain.InspectIssued;
import com.tocc.risk.domain.InspectTask;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.mapper.InspectIssuedMapper;
import com.tocc.risk.mapper.InspectTaskMapper;
import com.tocc.risk.mapper.PitfallsMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.ApproveMapper;
import com.tocc.risk.domain.Approve;
import com.tocc.risk.service.IApproveService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 检查审批Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class ApproveServiceImpl implements IApproveService 
{
    @Autowired
    private ApproveMapper approveMapper;
    @Autowired
    private InspectTaskMapper inspectTaskMapper;
    @Autowired
    private InspectIssuedMapper issuedMapper;
    @Autowired
    private PitfallsMapper pitfallsMapper;

    /**
     * 查询检查审批
     * 
     * @param id 检查审批主键
     * @return 检查审批
     */
    @Override
    public Approve selectApproveById(String id)
    {
        return approveMapper.selectApproveById(id);
    }

    /**
     * 查询检查审批列表
     * 
     * @param pitfalls
     * @return 检查审批
     */
    @Override
    public List<Map> selectApproveList(Pitfalls pitfalls)
    {
        pitfalls.setApproveById(SecurityUtils.getLoginUser().getUserId());
        return approveMapper.selectApproveList(pitfalls);
    }

    /**
     * 查看审批详情
     * @param pitfalls
     * @return
     */
    @Override
    public Pitfalls getPitfallsInfo(Pitfalls pitfalls) {
        List<Pitfalls> list = pitfallsMapper.selectPitfallsList(pitfalls);
        return list.size() > 0 ? list.get(0) : null;
    }

    /**
     * 新增检查审批
     * 
     * @param approve 检查审批
     * @return 结果
     */
    @Override
    public int insertApprove(Approve approve)
    {
        return approveMapper.insertApprove(approve);
    }

    /**
     * 修改检查审批
     * 
     * @param approve 检查审批
     * @return 结果
     */
    @Override
    @Transactional
    public int updateApprove(Approve approve)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        approve.setApproveBy(loginUser.getUser().getNickName());
        Approve a = approveMapper.selectApproveById(approve.getId());
        // 判断是否同意或者驳回
        if (approve.getStatus() == 1) {
            // 通过，先判断是否是最后一部
            if (a.getStep().equals("2")) {
                // 最后一步，直接更新
                approve.setApproveTime(new Date());
                // 风险放置审批结果
                Pitfalls pit = pitfallsMapper.selectPitfallsById(a.getPitfallsId());
                pit.setIsApprove(1);
                pitfallsMapper.updatePitfalls(pit);
            } else {
                Pitfalls pit = pitfallsMapper.selectPitfallsById(a.getPitfallsId());
                // 第一步，需创建下一步审核人的审核记录
                Approve app = new Approve();
                app.setStatus(0);
                app.setStep("2");
                app.setTaskId(a.getTaskId());
                app.setIssuedId(a.getIssuedId());
                app.setPitfallsId(a.getPitfallsId());
                app.setApproveById(pit.getProvinceUnitId());
                app.setId(IdUtil.fastSimpleUUID());
                approveMapper.insertApprove(app);
            }
            // 计算进度
            setTaskProgress(a.getIssuedId());
        } else {
            // 驳回
            InspectTask task = inspectTaskMapper.selectInspectTaskById(a.getTaskId());
            task.setStatus(2);
            inspectTaskMapper.updateInspectTask(task);
            // 重新计算下发任务的完成进度
            setTaskProgress(task.getIssuedId());


            // 驳回更新
            approve.setApproveTime(new Date());
            approve.setApproveBy(loginUser.getUser().getNickName());
        }
        return approveMapper.updateApprove(approve);
    }

    /**
     * 计算下发任务的完成进度
     * @param issuedId
     */
    @Transactional
    private void setTaskProgress(String issuedId) {
        Map<String, BigDecimal> map = issuedMapper.sumProgress(issuedId);
        BigDecimal sum = map.get("suc").divide(map.get("num"), 2, BigDecimal.ROUND_HALF_UP);
        String progress = sum.multiply(BigDecimal.valueOf(100)) + "%";
        InspectIssued issued = new InspectIssued();
        issued.setId(issuedId);
        issued.setTaskProgress(progress);
        issuedMapper.updateInspectIssued(issued);
    }

    /**
     * 批量删除检查审批
     * 
     * @param ids 需要删除的检查审批主键
     * @return 结果
     */
    @Override
    public int deleteApproveByIds(Long[] ids)
    {
        return approveMapper.deleteApproveByIds(ids);
    }

    /**
     * 删除检查审批信息
     * 
     * @param id 检查审批主键
     * @return 结果
     */
    @Override
    public int deleteApproveById(String id)
    {
        return approveMapper.deleteApproveById(id);
    }
}
