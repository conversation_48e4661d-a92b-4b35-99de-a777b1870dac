package com.tocc.risk.service;

import java.util.List;

import com.tocc.common.core.domain.AjaxResult;
import com.tocc.risk.domain.ModifyTask;

/**
 * 整改任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IModifyTaskService 
{
    /**
     * 查询整改任务
     * 
     * @param id 整改任务主键
     * @return 整改任务
     */
    public ModifyTask selectModifyTaskById(Long id);

    /**
     * 查询整改任务列表
     * 
     * @param modifyTask 整改任务
     * @return 整改任务集合
     */
    public List<ModifyTask> selectModifyTaskList(ModifyTask modifyTask);

    /**
     * 新增整改任务
     * 
     * @param modifyTask 整改任务
     * @return 结果
     */
    public AjaxResult insertModifyTask(ModifyTask modifyTask);

    /**
     * 修改整改任务
     * 
     * @param modifyTask 整改任务
     * @return 结果
     */
    public int updateModifyTask(ModifyTask modifyTask);

    /**
     * 批量删除整改任务
     * 
     * @param ids 需要删除的整改任务主键集合
     * @return 结果
     */
    public int deleteModifyTaskByIds(Long[] ids);

    /**
     * 删除整改任务信息
     * 
     * @param id 整改任务主键
     * @return 结果
     */
    public int deleteModifyTaskById(Long id);
}
