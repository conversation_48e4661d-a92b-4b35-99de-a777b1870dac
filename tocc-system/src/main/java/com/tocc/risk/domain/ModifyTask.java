package com.tocc.risk.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 整改任务对象 risk_modify_task
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@ApiModel(value = "整改任务对象")
public class ModifyTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    @ApiModelProperty(value = "${comment}")
    private Long id;

    /** 任务编号 */
    @Excel(name = "任务编号")
    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    /** 隐患ID */
    @Excel(name = "隐患ID")
    @ApiModelProperty(value = "隐患ID")
    private String pitfallsId;

    /** 隐患ID */
    @ApiModelProperty(value = "隐患名称")
    private String name;

    /** 责任单位 */
    @Excel(name = "责任单位")
    @ApiModelProperty(value = "责任单位")
    private String dutyUnit;

    /** 责任单位 */
    @Excel(name = "责任单位ID")
    @ApiModelProperty(value = "责任单位ID")
    private String dutyUnitId;

    /** 责任人 */
    @Excel(name = "责任人")
    @ApiModelProperty(value = "责任人")
    private String dutyBy;

    /** 责任人ID */
    @Excel(name = "责任人ID")
    @ApiModelProperty(value = "责任人ID")
    private String dutyById;

    /** 整改期限 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "整改期限", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "整改期限")
    private Date entTime;

    /** 任务状态（0-待处理，1-整改中，2-已完成） */
    @Excel(name = "任务状态", readConverterExp = "0=-待处理，1-整改中，2-已完成")
    @ApiModelProperty(value = "任务状态")
    private String status;

    /** 整改进度/备注 */
    @Excel(name = "整改进度/备注")
    @ApiModelProperty(value = "整改进度/备注")
    private String remarks;

    /** 上传整改附件 */
    @Excel(name = "上传整改附件")
    @ApiModelProperty(value = "上传整改附件")
    private String fileUrls;

    /** 整改完成时间 */
    @Excel(name = "整改完成时间")
    @ApiModelProperty(value = "整改完成时间")
    private Date completeTime;

    /** 整改措施 */
    @Excel(name = "整改措施")
    @ApiModelProperty(value = "整改措施")
    private String measures;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    @ApiModelProperty(value = "创建人ID")
    private Long createById;

    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "创建人ID")
    private Integer delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTaskNo(String taskNo) 
    {
        this.taskNo = taskNo;
    }

    public String getTaskNo() 
    {
        return taskNo;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }

    public void setPitfallsId(String pitfallsId)
    {
        this.pitfallsId = pitfallsId;
    }

    public String getPitfallsId()
    {
        return pitfallsId;
    }

    public void setDutyUnit(String dutyUnit) 
    {
        this.dutyUnit = dutyUnit;
    }

    public String getDutyUnit() 
    {
        return dutyUnit;
    }

    public void setDutyUnitId(String dutyUnitId)
    {
        this.dutyUnitId = dutyUnitId;
    }

    public String getDutyUnitId()
    {
        return dutyUnitId;
    }

    public void setDutyBy(String dutyBy) 
    {
        this.dutyBy = dutyBy;
    }

    public String getDutyBy() 
    {
        return dutyBy;
    }

    public void setDutyById(String dutyById)
    {
        this.dutyById = dutyById;
    }

    public String getDutyById()
    {
        return dutyById;
    }

    public void setEntTime(Date entTime) 
    {
        this.entTime = entTime;
    }

    public Date getEntTime() 
    {
        return entTime;
    }

    public void setCompleteTime(Date completeTime)
    {
        this.completeTime = completeTime;
    }

    public Date getCompleteTime()
    {
        return completeTime;
    }

    public void setMeasures(String measures)
    {
        this.measures = measures;
    }

    public String getMeasures()
    {
        return measures;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }

    public void setFileUrls(String fileUrls) 
    {
        this.fileUrls = fileUrls;
    }

    public String getFileUrls() 
    {
        return fileUrls;
    }

    public void setCreateById(Long createById) 
    {
        this.createById = createById;
    }

    public Long getCreateById() 
    {
        return createById;
    }

    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskNo", getTaskNo())
            .append("pitfallsId", getPitfallsId())
            .append("dutyUnit", getDutyUnit())
            .append("dutyBy", getDutyBy())
            .append("entTime", getEntTime())
            .append("status", getStatus())
            .append("remarks", getRemarks())
            .append("fileUrls", getFileUrls())
            .append("completeTime", getCompleteTime())
            .append("measures", getMeasures())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("createById", getCreateById())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
