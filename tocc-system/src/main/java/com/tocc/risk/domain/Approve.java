package com.tocc.risk.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 检查审批对象 risk_approve
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
public class Approve extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    private String id;

    /** 隐患ID */
    @Excel(name = "隐患ID")
    private String pitfallsId;

    /** 任务下发ID */
    @Excel(name = "任务下发ID")
    private String issuedId;

    /** 任务填报ID */
    @Excel(name = "任务填报ID")
    private String taskId;

    /** 状态（0-待处理，1-通过，2-驳回） */
    @Excel(name = "状态", readConverterExp = "0=-待处理，1-通过，2-驳回")
    private Integer status;

    /** 审核人 */
    @Excel(name = "审核人")
    private String approveBy;

    /** 审核人ID */
    @Excel(name = "审核人ID")
    private String approveById;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date approveTime;

    /** 步骤(1-复核审核，2-省级审核) */
    @Excel(name = "步骤(1-复核审核，2-省级审核)")
    private String step;

    /** 下一个审核人ID */
    private String nextApproveById;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setPitfallsId(String pitfallsId)
    {
        this.pitfallsId = pitfallsId;
    }

    public String getPitfallsId()
    {
        return pitfallsId;
    }

    public void setIssuedId(String issuedId)
    {
        this.issuedId = issuedId;
    }

    public String getIssuedId()
    {
        return issuedId;
    }

    public void setTaskId(String taskId)
    {
        this.taskId = taskId;
    }

    public String getTaskId()
    {
        return taskId;
    }

    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setApproveBy(String approveBy) 
    {
        this.approveBy = approveBy;
    }

    public String getApproveBy() 
    {
        return approveBy;
    }

    public void setApproveById(String approveById)
    {
        this.approveById = approveById;
    }

    public String getApproveById()
    {
        return approveById;
    }

    public void setApproveTime(Date approveTime) 
    {
        this.approveTime = approveTime;
    }

    public Date getApproveTime() 
    {
        return approveTime;
    }

    public void setStep(String step) 
    {
        this.step = step;
    }

    public String getStep() 
    {
        return step;
    }

    public void setNextApproveById(String nextApproveById)
    {
        this.nextApproveById = nextApproveById;
    }

    public String getNextApproveById()
    {
        return nextApproveById;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pitfallsId", getPitfallsId())
            .append("issuedId", getIssuedId())
            .append("taskId", getTaskId())
            .append("status", getStatus())
            .append("approveBy", getApproveBy())
            .append("approveById", getApproveById())
            .append("approveTime", getApproveTime())
            .append("step", getStep())
            .toString();
    }
}
