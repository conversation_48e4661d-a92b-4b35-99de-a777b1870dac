package com.tocc.risk.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "应急一张图数据统计分析对象")
public class EmergencyMapStatisticsVO {


    @ApiModelProperty(value = "应急事件数量")
    private Integer emergencyEventQuantity;

    @ApiModelProperty(value = "救援队伍数量")
    private Integer rescueTeamQuantity;

    @ApiModelProperty(value = "物资储备点数量")
    private Integer materialQuantity;

    @ApiModelProperty(value = "校验超时数量")
    private Integer alarmInfoQuantity;
}
