package com.tocc.risk.vo;

import com.tocc.em.domain.EmWarehouse;
import com.tocc.risk.domain.ModifyTask;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.domain.Projects;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel(value = "风险一张图对象")
public class RiskHome {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目/隐患ID tyoe区分")
    private String id;

    @ApiModelProperty(value = "类型（1-隐患点，2-项目点）")
    private int type;

    @ApiModelProperty(value = "项目数据")
    private Projects projects;

    @ApiModelProperty(value = "项目隐患数据")
    private List<Pitfalls> pitfallsList;

    @ApiModelProperty(value = "隐患点数据")
    private Pitfalls pitfalls;

    @ApiModelProperty(value = "隐患点整改数据")
    private ModifyTask modifyTask;

    @ApiModelProperty(value = "应急物资点")
    private EmWarehouse warehouse;

    @ApiModelProperty(value = "应急物资点物资")
    private List<MaterialVO> materialList;

    @ApiModelProperty(value = "救援点")
    private RescueTeamVO rescueTeam;

    @ApiModelProperty(value = "救援点物资")
    private List<MaterialVO> materialTeamList;
}
