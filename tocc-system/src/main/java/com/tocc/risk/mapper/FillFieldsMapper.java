package com.tocc.risk.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tocc.risk.domain.FillFields;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 填报项字段Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Mapper
public interface FillFieldsMapper extends BaseMapper<FillFields>
{
    /**
     * 查询填报项字段
     * 
     * @param id 填报项字段主键
     * @return 填报项字段
     */
    public FillFields selectFillFieldsById(Long id);

    /**
     * 查询填报项字段列表
     * 
     * @param fillFields 填报项字段
     * @return 填报项字段集合
     */
    public List<FillFields> selectFillFieldsList(FillFields fillFields);

    /**
     * 查询填报项字段列表
     *
     * @param ids 填报项字段
     * @return 填报项字段集合
     */
    public List<FillFields> getListByIds(@Param("ids") String[] ids);

    /**
     * 新增填报项字段
     * 
     * @param fillFields 填报项字段
     * @return 结果
     */
    public int insertFillFields(FillFields fillFields);

    /**
     * 修改填报项字段
     * 
     * @param fillFields 填报项字段
     * @return 结果
     */
    public int updateFillFields(FillFields fillFields);

    /**
     * 删除填报项字段
     * 
     * @param id 填报项字段主键
     * @return 结果
     */
    public int deleteFillFieldsById(String id);

    /**
     * 批量删除填报项字段
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFillFieldsByIds(Long[] ids);
}
