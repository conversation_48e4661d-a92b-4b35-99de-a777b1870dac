package com.tocc.risk.mapper;

import java.util.List;

import com.tocc.risk.domain.InspectIssued;
import com.tocc.risk.domain.InspectTask;

/**
 * 检查任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface InspectTaskMapper 
{
    /**
     * 查询检查任务
     * 
     * @param id 检查任务主键
     * @return 检查任务
     */
    public InspectTask selectInspectTaskById(String id);

    /**
     * 查询检查任务列表
     * 
     * @param inspectTask 检查任务
     * @return 检查任务集合
     */
    public List<InspectIssued> selectInspectTaskList(InspectTask inspectTask);

    /**
     * 新增检查任务
     * 
     * @param inspectTask 检查任务
     * @return 结果
     */
    public int insertInspectTask(InspectTask inspectTask);

    /**
     * 修改检查任务
     * 
     * @param inspectTask 检查任务
     * @return 结果
     */
    public int updateInspectTask(InspectTask inspectTask);

    /**
     * 删除检查任务
     * 
     * @param id 检查任务主键
     * @return 结果
     */
    public int deleteInspectTaskById(String id);

    /**
     * 批量删除检查任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInspectTaskByIds(Long[] ids);
}
