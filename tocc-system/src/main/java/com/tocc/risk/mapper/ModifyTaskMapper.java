package com.tocc.risk.mapper;

import java.util.List;
import com.tocc.risk.domain.ModifyTask;

/**
 * 整改任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface ModifyTaskMapper 
{
    /**
     * 查询整改任务
     * 
     * @param id 整改任务主键
     * @return 整改任务
     */
    public ModifyTask selectModifyTaskById(Long id);

    /**
     * 查询整改任务列表
     * 
     * @param modifyTask 整改任务
     * @return 整改任务集合
     */
    public List<ModifyTask> selectModifyTaskList(ModifyTask modifyTask);

    /**
     * 新增整改任务
     * 
     * @param modifyTask 整改任务
     * @return 结果
     */
    public int insertModifyTask(ModifyTask modifyTask);

    /**
     * 修改整改任务
     * 
     * @param modifyTask 整改任务
     * @return 结果
     */
    public int updateModifyTask(ModifyTask modifyTask);

    /**
     * 删除整改任务
     * 
     * @param id 整改任务主键
     * @return 结果
     */
    public int deleteModifyTaskById(Long id);

    /**
     * 批量删除整改任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteModifyTaskByIds(Long[] ids);

    /**
     * 根据隐患点ID获取整改信息
     * @param id
     * @return
     */
    public ModifyTask selectByPitfallsId(String id);
}
