package com.tocc.risk.mapper;

import java.util.List;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.vo.WordFileVo;
import org.apache.ibatis.annotations.Param;

/**
 * 隐患列Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface PitfallsMapper 
{
    /**
     * 查询隐患列
     * 
     * @param id 隐患列主键
     * @return 隐患列
     */
    public Pitfalls selectPitfallsById(String id);

    /**
     * 查询隐患列列表
     * 
     * @param pitfalls 隐患列
     * @return 隐患列集合
     */
    public List<Pitfalls> selectPitfallsList(Pitfalls pitfalls);

    /**
     * 新增隐患列
     * 
     * @param pitfalls 隐患列
     * @return 结果
     */
    public int insertPitfalls(Pitfalls pitfalls);

    /**
     * 修改隐患列
     * 
     * @param pitfalls 隐患列
     * @return 结果
     */
    public int updatePitfalls(Pitfalls pitfalls);

    /**
     * 删除隐患列
     * 
     * @param id 隐患列主键
     * @return 结果
     */
    public int deletePitfallsById(String id);

    /**
     * 批量删除隐患列
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePitfallsByIds(Long[] ids);

    /**
     * 获取隐患总体情况统计
     * @return
     */
    public WordFileVo getOverallSituation();

    /**
     * 获取隐患总体情况统计
     * @return
     */
    public List<WordFileVo> getCitysSpread();

    /**
     * 获取隐患总体情况统计
     * @return
     */
    public WordFileVo getAreasSpread(@Param("title") String title, @Param("val") String val);

    /**
     * 获取隐患总体情况统计
     * @return
     */
    public WordFileVo getProgress();

    /**
     * 获取隐患总体情况统计
     * @return
     */
    public List<WordFileVo> getCityNotChange();

    public Pitfalls getPitfallsByTaskId(String taskId);
}
