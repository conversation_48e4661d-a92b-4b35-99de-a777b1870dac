package com.tocc.em.dto;

import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 应急预案附件管理DTO
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
@ApiModel(value = "应急预案附件管理DTO")
public class EmPrePlanFileDTO
{

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private String version;

    /** 关联业务ID */
    @Excel(name = "关联业务ID")
    @ApiModelProperty(value = "关联业务ID")
    private String bizId;

    /** 附件名称(含扩展名) */
    @Excel(name = "附件名称(含扩展名)")
    @ApiModelProperty(value = "附件名称(含扩展名)")
    private String fileName;

    /** 附件类型,字典：file_type */
    @Excel(name = "附件类型,字典：file_type")
    @ApiModelProperty(value = "附件类型,字典：file_type")
    private String fileType;

    /** 附件描述 */
    @Excel(name = "附件描述")
    @ApiModelProperty(value = "附件描述")
    private String fileDesc;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    /** 删除标志(0存在/1删除) */
    private Integer delFlag;

    /** 文件URL */
    @Excel(name = "文件URL")
    @ApiModelProperty(value = "文件URL")
    private String url;

    /** 文件大小 */
    @Excel(name = "文件大小")
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /** 文件原始名称 */
    @Excel(name = "文件原始名称")
    @ApiModelProperty(value = "文件原始名称")
    private String originalFileName;

    /** 文件新名字 */
    @Excel(name = "文件新名字")
    @ApiModelProperty(value = "文件新名字")
    private String newFileName;


}
