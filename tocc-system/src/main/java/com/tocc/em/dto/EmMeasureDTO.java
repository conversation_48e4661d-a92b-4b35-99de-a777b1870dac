package com.tocc.em.dto;

import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 应急响应处置措施DTO
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
@ApiModel(value = "应急响应处置措施DTO")
public class EmMeasureDTO
{

    /** ID主键 */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private String version;

    /** 关联预案ID */
    @Excel(name = "关联预案ID")
    @ApiModelProperty(value = "关联预案ID")
    private String prePlanId;

    /** 事件级别 */
    @Excel(name = "事件级别")
    @ApiModelProperty(value = "事件级别")
    private Integer eventLevel;

    /** 触发条件描述 */
    @Excel(name = "触发条件描述")
    @ApiModelProperty(value = "触发条件描述")
    private String triggerCondition;

    /** 处置措施详情 */
    @Excel(name = "处置措施详情")
    @ApiModelProperty(value = "处置措施详情")
    private String measureContent;

    /** 是否派遣工作组(0否1是) */
    @Excel(name = "是否派遣工作组(0否1是)")
    @ApiModelProperty(value = "是否派遣工作组")
    private Integer isDispatchTeam;

    /** 需上报上级(Ⅱ级以上1是) */
    @Excel(name = "需上报上级(Ⅱ级以上1是)")
    @ApiModelProperty(value = "需上报上级")
    private Integer needReport;

    /** 需调度资源类型数组 */
    @Excel(name = "需调度资源类型数组")
    @ApiModelProperty(value = "需调度资源类型数组")
    private String resourceTypes;

    /** 专项作业JSON数组 */
    @Excel(name = "专项作业JSON数组")
    @ApiModelProperty(value = "专项作业JSON数组")
    private String specialOperations;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updater;

    /** 删除标志(0存在1删除) */
    @Excel(name = "更新人")
    @ApiModelProperty(value = "删除标志(0存在1删除)")
    private Integer isDeleted;


}
