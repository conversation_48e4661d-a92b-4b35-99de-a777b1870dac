package com.tocc.em.service.impl;

import java.util.List;

import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmEnterprisePersonnelMapper;
import com.tocc.em.domain.EmEnterprisePersonnel;
import com.tocc.em.service.IEmEnterprisePersonnelService;

/**
 * 企业人员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
public class EmEnterprisePersonnelServiceImpl implements IEmEnterprisePersonnelService 
{
    @Autowired
    private EmEnterprisePersonnelMapper emEnterprisePersonnelMapper;

    /**
     * 查询企业人员信息
     * 
     * @param enterprisePersonnelId 企业人员信息主键
     * @return 企业人员信息
     */
    @Override
    public EmEnterprisePersonnel selectEmEnterprisePersonnelByEnterprisePersonnelId(String enterprisePersonnelId)
    {
        return emEnterprisePersonnelMapper.selectEmEnterprisePersonnelByEnterprisePersonnelId(enterprisePersonnelId);
    }

    /**
     * 查询企业人员信息列表
     * 
     * @param emEnterprisePersonnel 企业人员信息
     * @return 企业人员信息
     */
    @Override
    public List<EmEnterprisePersonnel> selectEmEnterprisePersonnelList(EmEnterprisePersonnel emEnterprisePersonnel)
    {
        return emEnterprisePersonnelMapper.selectEmEnterprisePersonnelList(emEnterprisePersonnel);
    }

    /**
     * 新增企业人员信息
     * 
     * @param emEnterprisePersonnel 企业人员信息
     * @return 结果
     */
    @Override
    public int insertEmEnterprisePersonnel(EmEnterprisePersonnel emEnterprisePersonnel)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String userName = loginUser.getUser().getUserName();
        emEnterprisePersonnel.setCreator(userName);
        emEnterprisePersonnel.setUpdater(userName);
        emEnterprisePersonnel.setEnterprisePersonnelId(IdUtils.fastSimpleUUID());
        return emEnterprisePersonnelMapper.insertEmEnterprisePersonnel(emEnterprisePersonnel);
    }

    /**
     * 修改企业人员信息
     * 
     * @param emEnterprisePersonnel 企业人员信息
     * @return 结果
     */
    @Override
    public int updateEmEnterprisePersonnel(EmEnterprisePersonnel emEnterprisePersonnel)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String userName = loginUser.getUser().getUserName();
        emEnterprisePersonnel.setUpdater(userName);
        return emEnterprisePersonnelMapper.updateEmEnterprisePersonnel(emEnterprisePersonnel);
    }

    /**
     * 批量删除企业人员信息
     * 
     * @param enterprisePersonnelIds 需要删除的企业人员信息主键
     * @return 结果
     */
    @Override
    public int deleteEmEnterprisePersonnelByEnterprisePersonnelIds(String[] enterprisePersonnelIds)
    {
        for (String enterprisePersonnelId : enterprisePersonnelIds) {
            emEnterprisePersonnelMapper.deleteEmEnterprisePersonnelByEnterprisePersonnelId(enterprisePersonnelId);
        }
        return enterprisePersonnelIds.length;
    }

    /**
     * 删除企业人员信息信息
     * 
     * @param enterprisePersonnelId 企业人员信息主键
     * @return 结果
     */
    @Override
    public int deleteEmEnterprisePersonnelByEnterprisePersonnelId(String enterprisePersonnelId)
    {
        return emEnterprisePersonnelMapper.deleteEmEnterprisePersonnelByEnterprisePersonnelId(enterprisePersonnelId);
    }

    /**
     * 批量导入企业人员信息
     * 
     * @param enterprisePersonnelList 企业人员信息列表
     * @return 导入成功的记录数
     */
    @Override
    public int batchImportEmEnterprisePersonnel(List<EmEnterprisePersonnel> enterprisePersonnelList) {
        if (enterprisePersonnelList == null || enterprisePersonnelList.isEmpty()) {
            return 0;
        }

        LoginUser loginUser = SecurityUtils.getLoginUser();
        String userName = loginUser.getUser().getUserName();
        int successCount = 0;

        for (EmEnterprisePersonnel personnel : enterprisePersonnelList) {
            try {
                // 设置基础信息
                personnel.setEnterprisePersonnelId(IdUtils.fastSimpleUUID());
                personnel.setCreator(userName);
                personnel.setUpdater(userName);
                personnel.setCreateTime(DateUtils.getNowDate());
                personnel.setUpdateTime(DateUtils.getNowDate());
                personnel.setDelFlag(0);

                // 数据校验和清理
                if (personnel.getEnterpriseName() != null) {
                    personnel.setEnterpriseName(personnel.getEnterpriseName().trim());
                }
                if (personnel.getPrincipal() != null) {
                    personnel.setPrincipal(personnel.getPrincipal().trim());
                }
                if (personnel.getContactWay() != null) {
                    personnel.setContactWay(personnel.getContactWay().trim());
                }

                // 插入数据
                int result = emEnterprisePersonnelMapper.insertEmEnterprisePersonnel(personnel);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                // 记录导入失败的数据，继续处理其他数据
                System.err.println("导入企业人员数据失败：" + personnel.getEnterpriseName() + " - " + e.getMessage());
            }
        }

        return successCount;
    }
}
