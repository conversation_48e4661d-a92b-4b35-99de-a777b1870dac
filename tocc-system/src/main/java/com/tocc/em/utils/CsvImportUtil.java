package com.tocc.em.utils;

import com.tocc.em.domain.EmEnterprisePersonnel;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * CSV导入工具类
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public class CsvImportUtil {

    /**
     * 从CSV文件导入企业人员信息
     * 
     * @param csvFilePath CSV文件路径
     * @return 企业人员信息列表
     * @throws IOException 文件读取异常
     */
    public static List<EmEnterprisePersonnel> importFromCsv(String csvFilePath) throws IOException {
        List<EmEnterprisePersonnel> personnelList = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(csvFilePath), StandardCharsets.UTF_8))) {
            
            String line;
            boolean isFirstLine = true;
            
            while ((line = reader.readLine()) != null) {
                // 跳过标题行
                if (isFirstLine) {
                    isFirstLine = false;
                    continue;
                }
                
                // 跳过空行
                if (StringUtils.isBlank(line)) {
                    continue;
                }
                
                EmEnterprisePersonnel personnel = parseCsvLine(line);
                if (personnel != null) {
                    personnelList.add(personnel);
                }
            }
        }
        
        return personnelList;
    }

    /**
     * 从CSV内容导入企业人员信息
     * 
     * @param csvContent CSV文件内容
     * @return 企业人员信息列表
     */
    public static List<EmEnterprisePersonnel> importFromCsvContent(String csvContent) {
        List<EmEnterprisePersonnel> personnelList = new ArrayList<>();
        
        if (StringUtils.isBlank(csvContent)) {
            return personnelList;
        }
        
        String[] lines = csvContent.split("\n");
        boolean isFirstLine = true;
        
        for (String line : lines) {
            // 跳过标题行
            if (isFirstLine) {
                isFirstLine = false;
                continue;
            }
            
            // 跳过空行
            if (StringUtils.isBlank(line)) {
                continue;
            }
            
            EmEnterprisePersonnel personnel = parseCsvLine(line);
            if (personnel != null) {
                personnelList.add(personnel);
            }
        }
        
        return personnelList;
    }

    /**
     * 解析CSV行数据
     * 
     * @param csvLine CSV行数据
     * @return 企业人员信息对象
     */
    private static EmEnterprisePersonnel parseCsvLine(String csvLine) {
        if (StringUtils.isBlank(csvLine)) {
            return null;
        }
        
        // 处理CSV格式，支持逗号分隔，考虑引号包围的字段
        String[] fields = parseCsvFields(csvLine);
        
        // 根据CSV文件的列结构解析，假设格式为：企业名称,负责人姓名,联系电话
        if (fields.length < 3) {
            System.err.println("CSV行数据格式不正确，至少需要3列：" + csvLine);
            return null;
        }
        
        EmEnterprisePersonnel personnel = new EmEnterprisePersonnel();
        
        // 企业名称（第1列）
        personnel.setEnterpriseName(cleanField(fields[0]));
        
        // 负责人姓名（第2列）
        personnel.setPrincipal(cleanField(fields[1]));
        
        // 联系电话（第3列）
        personnel.setContactWay(cleanField(fields[2]));
        
        // 验证必填字段
        if (StringUtils.isBlank(personnel.getEnterpriseName())) {
            System.err.println("企业名称不能为空：" + csvLine);
            return null;
        }
        
        return personnel;
    }

    /**
     * 解析CSV字段，处理引号和逗号
     * 
     * @param csvLine CSV行数据
     * @return 字段数组
     */
    private static String[] parseCsvFields(String csvLine) {
        List<String> fields = new ArrayList<>();
        boolean inQuotes = false;
        StringBuilder currentField = new StringBuilder();
        
        for (int i = 0; i < csvLine.length(); i++) {
            char c = csvLine.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        // 添加最后一个字段
        fields.add(currentField.toString());
        
        return fields.toArray(new String[0]);
    }

    /**
     * 清理字段内容
     * 
     * @param field 原始字段
     * @return 清理后的字段
     */
    private static String cleanField(String field) {
        if (field == null) {
            return null;
        }
        
        // 去除首尾空格和引号
        field = field.trim();
        if (field.startsWith("\"") && field.endsWith("\"")) {
            field = field.substring(1, field.length() - 1);
        }
        
        return field.trim();
    }

    /**
     * 生成CSV模板内容
     * 
     * @return CSV模板内容
     */
    public static String generateCsvTemplate() {
        StringBuilder template = new StringBuilder();
        template.append("企业名称,负责人姓名,联系电话\n");
        template.append("示例企业有限公司,张三,13800138000\n");
        template.append("测试科技公司,李四,13900139000\n");
        return template.toString();
    }
} 