package com.tocc.em.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 应急预案组织体系对象 em_pre_plan_dept
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
@ApiModel(value = "应急预案组织体系对象")
public class EmPrePlanDept extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */

    private String id;

    @ApiModelProperty(value = "显示排序")
    private Integer orderNum;

    /** 上级机构ID */
    @Excel(name = "上级机构ID")
    private String parentId;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    /** 关联预案ID */
    @Excel(name = "关联预案ID")
    private String prePlanId;

    /** 机构名称 */
    @Excel(name = "机构名称")
    private String deptName;

    /** 机构级别,0顶级机构，1下级机构 */
    @Excel(name = "机构级别,0顶级机构，1下级机构")
    private Integer deptLevel;

    /** 主要职责 */
    @Excel(name = "主要职责")
    private String deptJob;

    /** 关联事件级别 */
    @Excel(name = "关联事件级别")
    private String eventLevel;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    /** 删除标志(0存在/1删除) */
    private Integer delFlag;


}
