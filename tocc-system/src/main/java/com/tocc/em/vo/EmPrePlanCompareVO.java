package com.tocc.em.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 应急预案版本比较结果VO
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class EmPrePlanCompareVO {

    @ApiModelProperty(value = "预案ID")
    private String prePlanId;

    @ApiModelProperty(value = "预案名称")
    private String planName;

    @ApiModelProperty(value = "当前版本号")
    private String currentVersion;

    @ApiModelProperty(value = "比较的历史版本号")
    private String historyVersion;

    @ApiModelProperty(value = "基础信息差异列表")
    private List<FieldDifference> basicDifferences;

    @ApiModelProperty(value = "事件分级与响应差异")
    private List<AssociatedDataDifference> levelDifferences;

    @ApiModelProperty(value = "应急组织体系差异")
    private List<AssociatedDataDifference> deptDifferences;

    @ApiModelProperty(value = "处置措施差异")
    private List<AssociatedDataDifference> measureDifferences;

    @ApiModelProperty(value = "附件差异")
    private List<AssociatedDataDifference> fileDifferences;

    /**
     * 字段差异
     */
    @Data
    public static class FieldDifference {
        @ApiModelProperty(value = "字段名称")
        private String fieldName;

        @ApiModelProperty(value = "字段中文名")
        private String fieldLabel;

        @ApiModelProperty(value = "当前版本值")
        private String currentValue;

        @ApiModelProperty(value = "历史版本值")
        private String historyValue;

        @ApiModelProperty(value = "是否有差异")
        private Boolean isDifferent;
    }

    /**
     * 关联数据差异
     */
    @Data
    public static class AssociatedDataDifference {
        @ApiModelProperty(value = "数据类型")
        private String dataType;

        @ApiModelProperty(value = "操作类型：ADDED(新增)、DELETED(删除)、MODIFIED(修改)、UNCHANGED(未变更)")
        private String operationType;

        @ApiModelProperty(value = "数据ID")
        private String dataId;

        @ApiModelProperty(value = "数据标识")
        private String dataIdentifier;

        @ApiModelProperty(value = "当前版本数据")
        private Object currentData;

        @ApiModelProperty(value = "历史版本数据")
        private Object historyData;

        @ApiModelProperty(value = "字段级别的差异（当操作类型为MODIFIED时）")
        private List<FieldDifference> fieldDifferences;

        @ApiModelProperty(value = "子差异列表（用于嵌套结构，如部门下的用户差异）")
        private List<AssociatedDataDifference> children;
    }
} 