<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.RiskProjectsMapper">
    
    <resultMap type="Projects" id="RiskProjectsResult">
        <result property="id"    column="id"    />
        <result property="residentName"    column="resident_name"    />
        <result property="residentType"    column="resident_type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="projectName"    column="project_name"    />
        <result property="projectType"    column="project_type"    />
        <result property="buildUnit"    column="build_unit"    />
        <result property="constructionUnit"    column="construction_unit"    />
        <result property="address"    column="address"    />
        <result property="area"    column="area"    />
        <result property="residentsNum"    column="residents_num"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="roomType"    column="room_type"    />
        <result property="headInv"    column="head_inv"    />
        <result property="isCliff"    column="is_cliff"    />
        <result property="isCollapse"    column="is_collapse"    />
        <result property="isRelocate"    column="is_relocate"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="whistling"    column="whistling"    />
        <result property="whistlingTel"    column="whistling_tel"    />
        <result property="builder"    column="builder"    />
        <result property="builderId"    column="builder_id"    />
        <result property="builderTel"    column="builder_tel"    />
        <result property="construc"    column="construc"    />
        <result property="construcTel"    column="construc_tel"    />
        <result property="addresser"    column="addresser"    />
        <result property="addresserTel"    column="addresser_tel"    />
        <result property="countyer"    column="countyer"    />
        <result property="countyerTel"    column="countyer_tel"    />
        <result property="marketer"    column="marketer"    />
        <result property="marketerTel"    column="marketer_tel"    />
        <result property="provincer"    column="provincer"    />
        <result property="provincerTel"    column="provincer_tel"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createById"    column="create_by_id"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectRiskProjectsVo">
        select id, resident_name, resident_type, coordinate, project_name, project_type, build_unit, construction_unit,
        address, area, residents_num, risk_level, room_type, head_inv, is_cliff, is_collapse, is_relocate, start_time,
        end_time, whistling, whistling_tel, builder, builder_id, builder_tel, construc, construc_tel, addresser,
        addresser_tel, countyer, countyer_tel, marketer, marketer_tel, provincer, provincer_tel, status, remark,
        create_time, update_time, create_by, create_by_id, del_flag from risk_projects
    </sql>

    <select id="selectRiskProjectsList" parameterType="Projects" resultMap="RiskProjectsResult">
        <include refid="selectRiskProjectsVo"/>
        <where>
            del_flag = 0
            <if test="residentName != null  and residentName != ''"> and resident_name like concat('%', #{residentName}, '%')</if>
            <if test="residentType != null  and residentType != ''"> and resident_type = #{residentType}</if>
            <if test="coordinate != null  and coordinate != ''"> and coordinate = #{coordinate}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectType != null  and projectType != ''"> and project_type = #{projectType}</if>
            <if test="proTypes != null"> and project_type in
                <foreach item="type" collection="proTypes" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="units != null">
                and (
                <foreach item="unit" collection="units" open="(" separator="or" close=")">
                    build_unit = #{unit} or construction_unit = #{unit}
                </foreach>
                )
            </if>
            <if test="buildUnit != null  and buildUnit != ''"> and build_unit = #{buildUnit}</if>
            <if test="constructionUnit != null  and constructionUnit != ''"> and construction_unit = #{constructionUnit}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="residentsNum != null "> and residents_num = #{residentsNum}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="roomType != null  and roomType != ''"> and room_type = #{roomType}</if>
            <if test="headInv != null "> and head_inv = #{headInv}</if>
            <if test="isCliff != null "> and is_cliff = #{isCliff}</if>
            <if test="isCollapse != null "> and is_collapse = #{isCollapse}</if>
            <if test="isRelocate != null "> and is_relocate = #{isRelocate}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="whistling != null  and whistling != ''"> and whistling = #{whistling}</if>
            <if test="whistlingTel != null  and whistlingTel != ''"> and whistling_tel = #{whistlingTel}</if>
            <if test="builder != null  and builder != ''"> and builder = #{builder}</if>
            <if test="builderId != null"> and builder_id = #{builderId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createById != null "> and create_by_id = #{createById}</if>
        </where>
    </select>

    <select id="selectRiskProjectsById" resultMap="RiskProjectsResult">
        <include refid="selectRiskProjectsVo"/>
        where id = #{id}
    </select>

    <insert id="insertRiskProjects" parameterType="Projects">
        insert into risk_projects
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="residentName != null">resident_name,</if>
            <if test="residentType != null">resident_type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="projectName != null">project_name,</if>
            <if test="projectType != null">project_type,</if>
            <if test="buildUnit != null">build_unit,</if>
            <if test="constructionUnit != null">construction_unit,</if>
            <if test="address != null">address,</if>
            <if test="area != null">area,</if>
            <if test="residentsNum != null">residents_num,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="roomType != null">room_type,</if>
            <if test="headInv != null">head_inv,</if>
            <if test="isCliff != null">is_cliff,</if>
            <if test="isCollapse != null">is_collapse,</if>
            <if test="isRelocate != null">is_relocate,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="whistling != null">whistling,</if>
            <if test="whistlingTel != null">whistling_tel,</if>
            <if test="builder != null">builder,</if>
            <if test="builderId != null">builder_id,</if>
            <if test="builderTel != null">builder_tel,</if>
            <if test="construc != null">construc,</if>
            <if test="construcTel != null">construc_tel,</if>
            <if test="addresser != null">addresser,</if>
            <if test="addresserTel != null">addresser_tel,</if>
            <if test="countyer != null">countyer,</if>
            <if test="countyerTel != null">countyer_tel,</if>
            <if test="marketer != null">marketer,</if>
            <if test="marketerTel != null">marketer_tel,</if>
            <if test="provincer != null">provincer,</if>
            <if test="provincerTel != null">provincer_tel,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="residentName != null">#{residentName},</if>
            <if test="residentType != null">#{residentType},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="projectType != null">#{projectType},</if>
            <if test="buildUnit != null">#{buildUnit},</if>
            <if test="constructionUnit != null">#{constructionUnit},</if>
            <if test="address != null">#{address},</if>
            <if test="area != null">#{area},</if>
            <if test="residentsNum != null">#{residentsNum},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="roomType != null">#{roomType},</if>
            <if test="headInv != null">#{headInv},</if>
            <if test="isCliff != null">#{isCliff},</if>
            <if test="isCollapse != null">#{isCollapse},</if>
            <if test="isRelocate != null">#{isRelocate},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="whistling != null">#{whistling},</if>
            <if test="whistlingTel != null">#{whistlingTel},</if>
            <if test="builder != null">#{builder},</if>
            <if test="builderId != null">#{builderId},</if>
            <if test="builderTel != null">#{builderTel},</if>
            <if test="construc != null">#{construc},</if>
            <if test="construcTel != null">#{construcTel},</if>
            <if test="addresser != null">#{addresser},</if>
            <if test="addresserTel != null">#{addresserTel},</if>
            <if test="countyer != null">#{countyer},</if>
            <if test="countyerTel != null">#{countyerTel},</if>
            <if test="marketer != null">#{marketer},</if>
            <if test="marketerTel != null">#{marketerTel},</if>
            <if test="provincer != null">#{provincer},</if>
            <if test="provincerTel != null">#{provincerTel},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createById != null">#{createById},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateRiskProjects" parameterType="Projects">
        update risk_projects
        <trim prefix="SET" suffixOverrides=",">
            <if test="residentName != null">resident_name = #{residentName},</if>
            <if test="residentType != null">resident_type = #{residentType},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="projectType != null">project_type = #{projectType},</if>
            <if test="buildUnit != null">build_unit = #{buildUnit},</if>
            <if test="constructionUnit != null">construction_unit = #{constructionUnit},</if>
            <if test="address != null">address = #{address},</if>
            <if test="area != null">area = #{area},</if>
            <if test="residentsNum != null">residents_num = #{residentsNum},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="roomType != null">room_type = #{roomType},</if>
            <if test="headInv != null">head_inv = #{headInv},</if>
            <if test="isCliff != null">is_cliff = #{isCliff},</if>
            <if test="isCollapse != null">is_collapse = #{isCollapse},</if>
            <if test="isRelocate != null">is_relocate = #{isRelocate},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="whistling != null">whistling = #{whistling},</if>
            <if test="whistlingTel != null">whistling_tel = #{whistlingTel},</if>
            <if test="builder != null">builder = #{builder},</if>
            <if test="builderId != null">builder_id = #{builderId},</if>
            <if test="builderTel != null">builder_tel = #{builderTel},</if>
            <if test="construc != null">construc = #{construc},</if>
            <if test="construcTel != null">construc_tel = #{construcTel},</if>
            <if test="addresser != null">addresser = #{addresser},</if>
            <if test="addresserTel != null">addresser_tel = #{addresserTel},</if>
            <if test="countyer != null">countyer = #{countyer},</if>
            <if test="countyerTel != null">countyer_tel = #{countyerTel},</if>
            <if test="marketer != null">marketer = #{marketer},</if>
            <if test="marketerTel != null">marketer_tel = #{marketerTel},</if>
            <if test="provincer != null">provincer = #{provincer},</if>
            <if test="provincerTel != null">provincer_tel = #{provincerTel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRiskProjectsById" parameterType="Long">
        delete from risk_projects where id = #{id}
    </delete>

    <delete id="deleteRiskProjectsByIds" parameterType="String">
        delete from risk_projects where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>