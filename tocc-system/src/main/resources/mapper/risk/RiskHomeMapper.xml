<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.RiskHomeMapper">


    <select id="getMaterialByWarId" resultType="com.tocc.risk.vo.MaterialVO">
        select em.* from em_warehouse_material wm
        left join em_material em on wm.material_id = em.id
        where wm.warehouse_id = #{id}
    </select>

    <select id="getMaterialByTeamId" resultType="com.tocc.risk.vo.MaterialVO">
        select em.* from rescue_team_material tm
        left join em_material em on tm.material_id = em.id
        where tm.team_id = #{id}
    </select>

    <select id="selectRescueTeamList" resultType="com.tocc.risk.vo.RescueTeamVO">
        select
        rt.id, rt.team_name, rt.team_code, rt.address, rt.longitude, rt.latitude,
        rt.team_size, rt.leader_name, rt.leader_phone, rt.jurisdiction_unit,
        rt.jurisdiction_leader, rt.jurisdiction_phone, rt.team_type, rt.specialties,
        rt.status, rt.remark, rt.create_time, rt.creator, rt.update_time, rt.updater,
        -- 字典值转换
        dt1.dict_label as team_type_name,
        case rt.status
        when 1 then '正常'
        when 2 then '停用'
        else '未知'
        end as status_name
        from rescue_team rt
        left join sys_dict_data dt1 on dt1.dict_value = rt.team_type and dt1.dict_type = 'rescue_team_type'
        where rt.del_flag = 0
    </select>

    <select id="getIssuedList" resultType="java.util.HashMap">
        select name label, id value from risk_inspect_issued where del_flag = 0
    </select>

    <select id="getAreasList" resultType="java.util.Map">
        select dd.dict_label label, dd.dict_value value from
        (select area from risk_inspect_task where issued_id = #{issuedId} group by area) it
        left join sys_dict_data dd
        on it.area = dd.dict_value and dict_type = 'area_type'
    </select>

    <select id="getPitfallReprots" resultType="java.util.Map">
        select count(*) allPitfall,
        sum(case when rp.risk_level = 1 then 1 else 0 end) hPitfall,
        sum(case when mt.status = 1 then 1 else 0 end) isTru
        from risk_pitfalls rp
        left join risk_modify_task mt on mt.pitfalls_id = rp.id
        where rp.is_approve = 1 and rp.is_pitfalls = 1 and rp.del_flag = 0 and rp.create_by_id in (
        select su.user_id from sys_dept sd
        left join sys_user su on su.dept_id = sd.dept_id
        where ancestors like concat(#{anc}, '%'))
    </select>

    <select id="getProjectReprots" resultType="java.util.Map">
        select count(*) allPro from risk_projects pr
        where builder_id in (
        select su.user_id from sys_dept sd
        left join sys_user su on su.dept_id = sd.dept_id
        where ancestors like concat(#{anc}, '%'))
    </select>

    <select id="selectPitfallsList" resultType="java.util.Map">
        select rp.*, it.issued_id, sd.dept_name
        from risk_inspect_issued ii
        left join risk_inspect_task it on it.issued_id = ii.id
        left join risk_pitfalls rp on it.id = rp.task_id
        left join sys_user u on it.informant_id = u.user_id
        left join sys_dept sd on u.org_id = sd.dept_id
        <where>
            rp.del_flag = 0 and rp.is_pitfalls = 1 and is_approve = 1
            <if test="taskId != null"> and rp.task_id = #{taskId}</if>
            <if test="isApprove != null"> and rp.is_approve = #{isApprove}</if>
            <if test="riskLevel != null "> and rp.risk_level = #{riskLevel}</if>
            <if test="specialVoList.size() > 0 ">
                and
                <foreach collection="specialVoList" item="item" open="(" separator="," close=")">
                    (it.issued_id = #{item.issuedId} and it.area = #{item.area} and rp.inspect_type = #{item.inspectType})
                </foreach>
            </if>
            <if test="unitIds.length > 0 ">
                and
                <foreach collection="unitIds" item="item" open="(" separator="or" close=")">
                    ii.unit_id like concat(#{item}, '%')
                </foreach>
            </if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        group by id order by rp.create_time desc
    </select>
</mapper>