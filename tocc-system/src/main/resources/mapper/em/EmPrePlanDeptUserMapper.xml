<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.em.mapper.EmPrePlanDeptUserMapper">

    <resultMap type="EmPrePlanDeptUser" id="EmPrePlanDeptUserResult">
        <result property="id"    column="id"    />
        <result property="emDeptId"    column="em_dept_id"    />
        <result property="roleId"    column="role_id"    />
        <result property="leaderId"    column="leader_id"    />
        <result property="leaderName"    column="leader_name"    />
        <result property="depId"    column="dep_id"    />
        <result property="post"    column="post"    />
        <result property="contact"    column="contact"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmPrePlanDeptUserVo">
        select id, em_dept_id, role_id, leader_name,  leader_id,dep_id, post, contact, create_time, creator, update_time, updater, del_flag from em_pre_plan_dept_user
    </sql>

    <select id="selectEmPrePlanDeptUserList" parameterType="EmPrePlanDeptUser" resultMap="EmPrePlanDeptUserResult">
        select
            eppdu.id,
            eppdu.em_dept_id,
            eppdu.role_id,
            u.user_name leader_name,
            eppdu.leader_id,
            u.dept_id,
            eppdu.post,
            u.phonenumber contact,
            eppdu.create_time,
            eppdu.creator,
            eppdu.update_time,
            eppdu.updater,
            eppdu.del_flag
        from em_pre_plan_dept_user   eppdu
        left join sys_user u on eppdu.leader_id = u.user_id

        <where>
            eppdu.del_flag = 0 and u.del_flag = 0
            <if test="emDeptId != null  and emDeptId != ''"> and eppdu.em_dept_id = #{emDeptId}</if>
            <if test="roleId != null  and roleId != ''"> and eppdu.role_id = #{roleId}</if>
            <if test="leaderName != null  and leaderName != ''"> and eppdu.leader_name like concat('%', #{leaderName}, '%')</if>
            <if test="depId != null  and depId != ''"> and eppdu.dep_id = #{depId}</if>
            <if test="post != null  and post != ''"> and eppdu.post = #{post}</if>
            <if test="contact != null  and contact != ''"> and eppdu.contact = #{contact}</if>
            <if test="creator != null  and creator != ''"> and eppdu.creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and eppdu.updater = #{updater}</if>
            <if test="delFlag != null "> and eppdu.del_flag = #{delFlag}</if>
        </where>
    </select>

    <select id="selectEmPrePlanDeptUserById" parameterType="String" resultMap="EmPrePlanDeptUserResult">
        <include refid="selectEmPrePlanDeptUserVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <insert id="insertEmPrePlanDeptUser" parameterType="EmPrePlanDeptUser">
        insert into em_pre_plan_dept_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="emDeptId != null and emDeptId != ''">em_dept_id,</if>
            <if test="roleId != null and roleId != ''">role_id,</if>
            <if test="leaderName != null and leaderName != ''">leader_name,</if>
            <if test="leaderId != null and leaderId != ''">leader_id,</if>
            <if test="depId != null and depId != ''">dep_id,</if>
            <if test="post != null and post != ''">post,</if>
            <if test="contact != null">contact,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="emDeptId != null and emDeptId != ''">#{emDeptId},</if>
            <if test="roleId != null and roleId != ''">#{roleId},</if>
            <if test="leaderName != null and leaderName != ''">#{leaderName},</if>
            <if test="leaderId != null and leaderId != ''">#{leaderId},</if>
            <if test="depId != null and depId != ''">#{depId},</if>
            <if test="post != null and post != ''">#{post},</if>
            <if test="contact != null">#{contact},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmPrePlanDeptUser" parameterType="EmPrePlanDeptUser">
        update em_pre_plan_dept_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="emDeptId != null and emDeptId != ''">em_dept_id = #{emDeptId},</if>
            <if test="roleId != null and roleId != ''">role_id = #{roleId},</if>
            <if test="leaderName != null and leaderName != ''">leader_name = #{leaderName},</if>
            <if test="leaderId != null and leaderId != ''">leader_id = #{leaderId},</if>
            <if test="depId != null and depId != ''">dep_id = #{depId},</if>
            <if test="post != null and post != ''">post = #{post},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmPrePlanDeptUserById" parameterType="String">
        update  em_pre_plan_dept_user set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteEmPrePlanDeptUserByIds" parameterType="String">
        delete from em_pre_plan_dept_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
