package com.tocc.web.controller.system;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.system.domain.dto.ExpertInfoDTO;
import com.tocc.system.domain.vo.ExpertInfoVO;
import com.tocc.system.service.IExpertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 专家信息Controller
 *
 * <AUTHOR>
 */
@Api(tags = "专家信息管理")
@RestController
@RequestMapping("/system/expert")
public class ExpertController extends BaseController {

    @Autowired
    private IExpertService expertService;

    /**
     * 查询专家信息列表
     */
    @ApiOperation("查询专家信息列表")
    @PreAuthorize("@ss.hasPermi('system:expert:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody(required = false)  ExpertInfoDTO expertInfo) {
        startPage();
        List<ExpertInfoVO> list = expertService.selectExpertInfoList(expertInfo);
        return getDataTable(list);
    }

    /**
     * 导出专家信息列表
     */
    @ApiOperation("导出专家信息列表")
    @PreAuthorize("@ss.hasPermi('system:expert:export')")
    @Log(title = "专家信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody(required = false) ExpertInfoDTO expertInfo) {
        List<ExpertInfoVO> list = expertService.selectExpertInfoList(expertInfo);
        ExcelUtil<ExpertInfoVO> util = new ExcelUtil<ExpertInfoVO>(ExpertInfoVO.class);
        util.exportExcel(response, list, "专家信息数据");
    }

    /**
     * 获取专家信息详细信息
     */
    @ApiOperation("获取专家信息详细信息")
    @PreAuthorize("@ss.hasPermi('system:expert:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(value = "用户ID", required = true) @PathVariable("id") String id) {
        return success(expertService.selectExpertInfoById(id));
    }

    /**
     * 新增专家信息
     */
    @ApiOperation("新增专家信息")
    @PreAuthorize("@ss.hasPermi('system:expert:add')")
    @Log(title = "专家信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody ExpertInfoDTO expertInfo) {
        return toAjax(expertService.insertExpertInfo(expertInfo));
    }

    /**
     * 修改专家信息
     */
    @ApiOperation("修改专家信息")
    @PreAuthorize("@ss.hasPermi('system:expert:edit')")
    @Log(title = "专家信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExpertInfoDTO expertInfo) {
        return toAjax(expertService.updateExpertInfo(expertInfo));
    }

    /**
     * 删除专家信息
     */
    @ApiOperation("批量删除专家信息")
    @PreAuthorize("@ss.hasPermi('system:expert:remove')")
    @Log(title = "专家信息", businessType = BusinessType.DELETE)
    @PostMapping("/batchDel")
    public AjaxResult remove(@ApiParam(value = "用户ID数组", required = true) @RequestBody JSONObject jsonObject) {
        List<String> dataList = jsonObject.getList("dataList", String.class);
        if (CollectionUtil.isEmpty(dataList)) {
            return AjaxResult.error("删除失败，参数为空");
        }
        return toAjax(expertService.deleteExpertInfoByIds(dataList));
    }

    /**
     * 更新专家最后更新时间
     */
    @ApiOperation("更新专家最后更新时间")
    @PreAuthorize("@ss.hasPermi('system:expert:edit')")
    @Log(title = "专家信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updateTime/{id}")
    public AjaxResult updateLastUpdateTime(@ApiParam(value = "主键", required = true) @PathVariable String id) {
        return toAjax(expertService.updateLastUpdateTime(id));
    }
}
