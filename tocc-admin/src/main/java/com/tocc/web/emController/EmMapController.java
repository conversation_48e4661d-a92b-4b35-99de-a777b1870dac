package com.tocc.web.emController;


import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.service.IEmergencyMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "应急一张图")
@RestController
@RequestMapping("/emergency")
public class EmMapController extends BaseController {

    @Autowired
    private IEmergencyMapService emergencyMapService;

    /**
     * 获取统计数据
     * @return
     */
    @ApiOperation("获取统计数据分析")
    @PreAuthorize("@ss.hasPermi('emergencyMap:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
      return   success(emergencyMapService.getStatistics())  ;
    }



    
}
