package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.em.domain.EmEnterprisePersonnel;
import com.tocc.em.service.IEmEnterprisePersonnelService;
import com.tocc.em.utils.CsvImportUtil;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 企业人员信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Api(tags = "企业人员信息")
@RestController
@RequestMapping("/em/EnterprisePersonnel")
public class EmEnterprisePersonnelController extends BaseController
{
    @Autowired
    private IEmEnterprisePersonnelService emEnterprisePersonnelService;

    /**
     * 查询企业人员信息列表
     */
    @ApiOperation("查询企业人员信息列表")
    @PreAuthorize("@ss.hasPermi('em:EnterprisePersonnel:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmEnterprisePersonnel emEnterprisePersonnel)
    {
        startPage();
        List<EmEnterprisePersonnel> list = emEnterprisePersonnelService.selectEmEnterprisePersonnelList(emEnterprisePersonnel);
        return getDataTable(list);
    }

    /**
     * 导出企业人员信息列表
     */
    @ApiOperation("导出企业人员信息列表")
    @PreAuthorize("@ss.hasPermi('em:EnterprisePersonnel:export')")
    @Log(title = "企业人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmEnterprisePersonnel emEnterprisePersonnel)
    {
        List<EmEnterprisePersonnel> list = emEnterprisePersonnelService.selectEmEnterprisePersonnelList(emEnterprisePersonnel);
        ExcelUtil<EmEnterprisePersonnel> util = new ExcelUtil<EmEnterprisePersonnel>(EmEnterprisePersonnel.class);
        util.exportExcel(response, list, "企业人员信息数据");
    }

    /**
     * 获取企业人员信息详细信息
     */
    @ApiOperation("获取企业人员信息详细信息")
    @PreAuthorize("@ss.hasPermi('em:EnterprisePersonnel:query')")
    @GetMapping(value = "/{enterprisePersonnelId}")
    public AjaxResult getInfo(@PathVariable("enterprisePersonnelId") String enterprisePersonnelId)
    {
        return success(emEnterprisePersonnelService.selectEmEnterprisePersonnelByEnterprisePersonnelId(enterprisePersonnelId));
    }

    /**
     * 新增企业人员信息
     */
    @ApiOperation("新增企业人员信息")
    @PreAuthorize("@ss.hasPermi('em:EnterprisePersonnel:add')")
    @Log(title = "企业人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmEnterprisePersonnel emEnterprisePersonnel)
    {
        return toAjax(emEnterprisePersonnelService.insertEmEnterprisePersonnel(emEnterprisePersonnel));
    }

    /**
     * 修改企业人员信息
     */
    @ApiOperation("修改企业人员信息")
    @PreAuthorize("@ss.hasPermi('em:EnterprisePersonnel:edit')")
    @Log(title = "企业人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmEnterprisePersonnel emEnterprisePersonnel)
    {
        return toAjax(emEnterprisePersonnelService.updateEmEnterprisePersonnel(emEnterprisePersonnel));
    }

    /**
     * 删除企业人员信息
     */
    @ApiOperation("删除企业人员信息")
    @PreAuthorize("@ss.hasPermi('em:EnterprisePersonnel:remove')")
    @Log(title = "企业人员信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{enterprisePersonnelIds}")
    public AjaxResult remove(@PathVariable String[] enterprisePersonnelIds)
    {
        return toAjax(emEnterprisePersonnelService.deleteEmEnterprisePersonnelByEnterprisePersonnelIds(enterprisePersonnelIds));
    }

    /**
     * 下载CSV导入模板
     */
    @ApiOperation("下载CSV导入模板")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException
    {
        response.setContentType("text/csv;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=enterprise_personnel_template.csv");
        
        String template = CsvImportUtil.generateCsvTemplate();
        response.getOutputStream().write(template.getBytes(StandardCharsets.UTF_8));
        response.getOutputStream().flush();
    }

    /**
     * CSV文件导入企业人员信息
     */
    @ApiOperation("CSV文件导入企业人员信息")
    @PreAuthorize("@ss.hasPermi('em:EnterprisePersonnel:import')")
    @Log(title = "企业人员信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importCsv")
    public AjaxResult importCsv(@RequestParam("file") MultipartFile file) throws IOException
    {
        if (file.isEmpty()) {
            return error("上传文件不能为空");
        }

        // 检查文件类型
        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".csv")) {
            return error("请上传CSV格式的文件");
        }

        try {
            // 读取文件内容
            String csvContent = new String(file.getBytes(), StandardCharsets.UTF_8);
            
            // 解析CSV内容
            List<EmEnterprisePersonnel> personnelList = CsvImportUtil.importFromCsvContent(csvContent);
            
            if (personnelList.isEmpty()) {
                return error("CSV文件中没有有效的数据记录");
            }

            // 批量导入
            int successCount = emEnterprisePersonnelService.batchImportEmEnterprisePersonnel(personnelList);
            
            return success("成功导入 " + successCount + " 条记录，共处理 " + personnelList.size() + " 条数据");
            
        } catch (Exception e) {
            logger.error("CSV导入失败", e);
            return error("CSV导入失败：" + e.getMessage());
        }
    }

    /**
     * 通过指定文件路径导入CSV数据
     */
    @ApiOperation("通过指定文件路径导入CSV数据")
    @PreAuthorize("@ss.hasPermi('em:EnterprisePersonnel:import')")
    @Log(title = "企业人员信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importCsvByPath")
    public AjaxResult importCsvByPath(@RequestParam("filePath") String filePath)
    {
        try {
            // 从文件路径读取CSV
            List<EmEnterprisePersonnel> personnelList = CsvImportUtil.importFromCsv(filePath);
            
            if (personnelList.isEmpty()) {
                return error("CSV文件中没有有效的数据记录");
            }

            // 批量导入
            int successCount = emEnterprisePersonnelService.batchImportEmEnterprisePersonnel(personnelList);
            
            return success("成功导入 " + successCount + " 条记录，共处理 " + personnelList.size() + " 条数据");
            
        } catch (IOException e) {
            logger.error("CSV文件读取失败", e);
            return error("CSV文件读取失败：" + e.getMessage());
        } catch (Exception e) {
            logger.error("CSV导入失败", e);
            return error("CSV导入失败：" + e.getMessage());
        }
    }
}
