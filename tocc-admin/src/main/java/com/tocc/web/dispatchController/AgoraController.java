package com.tocc.web.dispatchController;

import com.tocc.common.annotation.Log;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.service.IAgoraService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api("声网接口")
@RestController
@RequestMapping("/dispatch/agora")
public class AgoraController {

    @Resource
    private IAgoraService agoraService;

    /**
     * 声网webhook事件回调
     */
    @ApiOperation("声网webhook事件回调")
    @Log(title = "声网webhook事件回调", businessType = BusinessType.OTHER)
    @PostMapping("/handleWebhook")
    public AjaxResult handleWebhook(@RequestBody String rawBody, @RequestHeader("Agora-Signature-V2") String signature) {
        return agoraService.handleWebhook(rawBody, signature);
    }

}
