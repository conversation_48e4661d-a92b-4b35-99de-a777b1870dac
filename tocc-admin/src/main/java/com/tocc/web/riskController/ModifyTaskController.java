package com.tocc.web.riskController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.risk.domain.ModifyTask;
import com.tocc.risk.service.IModifyTaskService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 整改任务Controller
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags = "整改任务")
@RestController
@RequestMapping("/risk/modifyTask")
public class ModifyTaskController extends BaseController
{
    @Autowired
    private IModifyTaskService modifyTaskService;

    /**
     * 查询整改任务列表
     */
    @ApiOperation("获取整改列表")
//    @PreAuthorize("@ss.hasPermi('system:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(ModifyTask modifyTask)
    {
        startPage();
        List<ModifyTask> list = modifyTaskService.selectModifyTaskList(modifyTask);
        return getDataTable(list);
    }

    /**
     * 导出整改任务列表
     */
    @ApiOperation("导出整改列表")
//    @PreAuthorize("@ss.hasPermi('system:task:export')")
    @Log(title = "整改任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ModifyTask modifyTask)
    {
        List<ModifyTask> list = modifyTaskService.selectModifyTaskList(modifyTask);
        ExcelUtil<ModifyTask> util = new ExcelUtil<ModifyTask>(ModifyTask.class);
        util.exportExcel(response, list, "整改任务数据");
    }

    /**
     * 获取整改任务详细信息
     */
    @ApiOperation("获取整改详情")
//    @PreAuthorize("@ss.hasPermi('system:task:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(modifyTaskService.selectModifyTaskById(id));
    }

    /**
     * 新增整改任务
     */
    @ApiOperation("新增整改任务")
//    @PreAuthorize("@ss.hasPermi('system:task:add')")
    @Log(title = "整改任务", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ModifyTask modifyTask)
    {
        return modifyTaskService.insertModifyTask(modifyTask);
    }

    /**
     * 修改整改任务
     */
    @ApiOperation("修改整改任务")
//    @PreAuthorize("@ss.hasPermi('system:task:edit')")
    @Log(title = "整改任务", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody ModifyTask modifyTask)
    {
        return toAjax(modifyTaskService.updateModifyTask(modifyTask));
    }

    /**
     * 删除整改任务
     */
    @ApiOperation("删除整改任务")
//    @PreAuthorize("@ss.hasPermi('system:task:remove')")
    @Log(title = "整改任务", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody ModifyTask task)
    {
        return toAjax(modifyTaskService.deleteModifyTaskById(task.getId()));
    }
}
