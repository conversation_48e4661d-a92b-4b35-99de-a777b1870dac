<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.weather.mapper.WeatherWarningNotificationMapper">
    
    <resultMap type="WeatherWarningNotification" id="WeatherWarningNotificationResult">
        <result property="warningId"            column="warning_id"            />
        <result property="contactUserId"        column="contact_user_id"       />
        <result property="contactUnitName"      column="contact_unit_name"     />
        <result property="contactDeptName"      column="contact_dept_name"     />
        <result property="contactPostName"      column="contact_post_name"     />
        <result property="contactUserName"      column="contact_user_name"     />
        <result property="contactPhone"         column="contact_phone"         />
        <result property="contactEmail"         column="contact_email"         />
        <result property="notificationTime"     column="notification_time"     />
        <result property="confirmStatus"        column="confirm_status"        />
        <result property="confirmTime"          column="confirm_time"          />
        <result property="confirmUserId"        column="confirm_user_id"       />
        <result property="confirmUserName"      column="confirm_user_name"     />
        <result property="timeoutMinutes"       column="timeout_minutes"       />
        <result property="isTimeout"            column="is_timeout"            />
        <result property="alarmId"              column="alarm_id"              />
        <result property="createTime"           column="create_time"           />
        <result property="updateTime"           column="update_time"           />
    </resultMap>

    <resultMap type="WeatherWarningProgressVO" id="WeatherWarningProgressResult">
        <result property="warningId"            column="warning_id"            />
        <result property="contactUserId"        column="contact_user_id"       />
        <result property="contactUnitName"      column="contact_unit_name"     />
        <result property="contactDeptName"      column="contact_dept_name"     />
        <result property="contactPostName"      column="contact_post_name"     />
        <result property="contactUserName"      column="contact_user_name"     />
        <result property="contactPhone"         column="contact_phone"         />
        <result property="notificationTime"     column="notification_time"     />
        <result property="confirmStatus"        column="confirm_status"        />
        <result property="confirmStatusLabel"   column="confirm_status_label"  />
        <result property="confirmTime"          column="confirm_time"          />
        <result property="confirmUserName"      column="confirm_user_name"     />
        <result property="timeoutMinutes"       column="timeout_minutes"       />
        <result property="isTimeout"            column="is_timeout"            />
        <result property="remainingMinutes"     column="remaining_minutes"     />
    </resultMap>

    <sql id="selectWeatherWarningNotificationVo">
        select warning_id, contact_user_id, contact_unit_name, contact_dept_name, contact_post_name, 
               contact_user_name, contact_phone, contact_email, notification_time, confirm_status, 
               confirm_time, confirm_user_id, confirm_user_name, timeout_minutes, is_timeout, 
               alarm_id, create_time, update_time 
        from weather_warning_notification
    </sql>

    <select id="selectWeatherWarningNotificationList" parameterType="String" resultMap="WeatherWarningNotificationResult">
        <include refid="selectWeatherWarningNotificationVo"/>
        where warning_id = #{warningId}
        order by notification_time desc
    </select>

    <select id="selectNotificationsByUserId" parameterType="Long" resultMap="WeatherWarningNotificationResult">
        <include refid="selectWeatherWarningNotificationVo"/>
        where contact_user_id = #{contactUserId}
        order by notification_time desc
    </select>
    
    <select id="selectWeatherWarningNotificationByIds" resultMap="WeatherWarningNotificationResult">
        <include refid="selectWeatherWarningNotificationVo"/>
        where warning_id = #{warningId} and contact_user_id = #{contactUserId}
    </select>

    <select id="selectNotificationProgress" parameterType="String" resultMap="WeatherWarningProgressResult">
        select warning_id, contact_user_id, contact_unit_name, contact_dept_name, contact_post_name, 
               contact_user_name, contact_phone, notification_time, confirm_status,
               case confirm_status when '0' then '未确认' when '1' then '已确认' else '未知' end as confirm_status_label,
               confirm_time, confirm_user_name, timeout_minutes, is_timeout,
               case 
                   when confirm_status = '1' then 0
                   when is_timeout = '1' then 0
                   else greatest(0, timeout_minutes - timestampdiff(minute, notification_time, now()))
               end as remaining_minutes
        from weather_warning_notification
        where warning_id = #{warningId}
        order by notification_time desc
    </select>

    <select id="selectTimeoutNotifications" resultMap="WeatherWarningNotificationResult">
        <include refid="selectWeatherWarningNotificationVo"/>
        where confirm_status = '0' 
        and is_timeout = '0'
        and timestampdiff(minute, notification_time, now()) >= timeout_minutes
    </select>

    <select id="selectUnconfirmedNotifications" parameterType="String" resultMap="WeatherWarningNotificationResult">
        <include refid="selectWeatherWarningNotificationVo"/>
        where warning_id = #{warningId} and confirm_status = '0'
        order by notification_time desc
    </select>

    <select id="selectNotificationStats" parameterType="String" resultType="map">
        select
            count(*) as total_notifications,
            sum(case when confirm_status = '1' then 1 else 0 end) as confirmed_notifications,
            sum(case when confirm_status = '0' then 1 else 0 end) as unconfirmed_notifications,
            sum(case when is_timeout = '1' then 1 else 0 end) as timeout_notifications
        from weather_warning_notification
        where warning_id = #{warningId}
    </select>

    <select id="selectNotificationList" parameterType="WeatherWarningNotificationDTO" resultMap="WeatherWarningNotificationResult">
        <include refid="selectWeatherWarningNotificationVo"/>
        <where>
            <if test="warningId != null and warningId != ''">
                and warning_id = #{warningId}
            </if>
            <if test="contactUserId != null">
                and contact_user_id = #{contactUserId}
            </if>
            <if test="contactUnitName != null and contactUnitName != ''">
                and contact_unit_name like concat('%', #{contactUnitName}, '%')
            </if>
            <if test="contactUserName != null and contactUserName != ''">
                and contact_user_name like concat('%', #{contactUserName}, '%')
            </if>
            <if test="confirmStatus != null and confirmStatus != ''">
                and confirm_status = #{confirmStatus}
            </if>
            <if test="isTimeout != null and isTimeout != ''">
                and is_timeout = #{isTimeout}
            </if>
        </where>
        order by notification_time desc
    </select>
        
    <insert id="insertWeatherWarningNotification" parameterType="WeatherWarningNotification">
        insert into weather_warning_notification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warningId != null">warning_id,</if>
            <if test="contactUserId != null">contact_user_id,</if>
            <if test="contactUnitName != null">contact_unit_name,</if>
            <if test="contactDeptName != null">contact_dept_name,</if>
            <if test="contactPostName != null">contact_post_name,</if>
            <if test="contactUserName != null">contact_user_name,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="notificationTime != null">notification_time,</if>
            <if test="confirmStatus != null">confirm_status,</if>
            <if test="confirmTime != null">confirm_time,</if>
            <if test="confirmUserId != null">confirm_user_id,</if>
            <if test="confirmUserName != null">confirm_user_name,</if>
            <if test="timeoutMinutes != null">timeout_minutes,</if>
            <if test="isTimeout != null">is_timeout,</if>
            <if test="alarmId != null">alarm_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warningId != null">#{warningId},</if>
            <if test="contactUserId != null">#{contactUserId},</if>
            <if test="contactUnitName != null">#{contactUnitName},</if>
            <if test="contactDeptName != null">#{contactDeptName},</if>
            <if test="contactPostName != null">#{contactPostName},</if>
            <if test="contactUserName != null">#{contactUserName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="notificationTime != null">#{notificationTime},</if>
            <if test="confirmStatus != null">#{confirmStatus},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="confirmUserId != null">#{confirmUserId},</if>
            <if test="confirmUserName != null">#{confirmUserName},</if>
            <if test="timeoutMinutes != null">#{timeoutMinutes},</if>
            <if test="isTimeout != null">#{isTimeout},</if>
            <if test="alarmId != null">#{alarmId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertWeatherWarningNotification" parameterType="list">
        insert into weather_warning_notification (
            warning_id, contact_user_id, contact_unit_name, contact_dept_name, contact_post_name, 
            contact_user_name, contact_phone, contact_email, notification_time, confirm_status, 
            timeout_minutes, is_timeout, create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.warningId}, #{item.contactUserId}, #{item.contactUnitName}, #{item.contactDeptName}, 
             #{item.contactPostName}, #{item.contactUserName}, #{item.contactPhone}, #{item.contactEmail}, 
             #{item.notificationTime}, #{item.confirmStatus}, #{item.timeoutMinutes}, #{item.isTimeout}, 
             #{item.createTime})
        </foreach>
    </insert>

    <update id="updateWeatherWarningNotification" parameterType="WeatherWarningNotification">
        update weather_warning_notification
        <trim prefix="SET" suffixOverrides=",">
            <if test="contactUnitName != null">contact_unit_name = #{contactUnitName},</if>
            <if test="contactDeptName != null">contact_dept_name = #{contactDeptName},</if>
            <if test="contactPostName != null">contact_post_name = #{contactPostName},</if>
            <if test="contactUserName != null">contact_user_name = #{contactUserName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="confirmStatus != null">confirm_status = #{confirmStatus},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="confirmUserId != null">confirm_user_id = #{confirmUserId},</if>
            <if test="confirmUserName != null">confirm_user_name = #{confirmUserName},</if>
            <if test="timeoutMinutes != null">timeout_minutes = #{timeoutMinutes},</if>
            <if test="isTimeout != null">is_timeout = #{isTimeout},</if>
            <if test="alarmId != null">alarm_id = #{alarmId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where warning_id = #{warningId} and contact_user_id = #{contactUserId}
    </update>

    <update id="confirmNotification">
        update weather_warning_notification 
        set confirm_status = '1', 
            confirm_time = now(), 
            confirm_user_id = #{confirmUserId}, 
            confirm_user_name = #{confirmUserName},
            update_time = now()
        where warning_id = #{warningId} and contact_user_id = #{contactUserId}
    </update>

    <update id="updateNotificationTimeout">
        update weather_warning_notification 
        set is_timeout = '1', 
            alarm_id = #{alarmId},
            update_time = now()
        where warning_id = #{warningId} and contact_user_id = #{contactUserId}
    </update>

    <delete id="deleteWeatherWarningNotificationByIds">
        delete from weather_warning_notification 
        where warning_id = #{warningId} and contact_user_id = #{contactUserId}
    </delete>

    <delete id="deleteWeatherWarningNotificationByWarningId" parameterType="String">
        delete from weather_warning_notification where warning_id = #{warningId}
    </delete>

    <delete id="deleteWeatherWarningNotificationByWarningIds" parameterType="String">
        delete from weather_warning_notification where warning_id in
        <foreach item="warningId" collection="array" open="(" separator="," close=")">
            #{warningId}
        </foreach>
    </delete>

    <select id="selectNotificationsByOrgId" parameterType="String" resultMap="WeatherWarningNotificationResult">
        <include refid="selectWeatherWarningNotificationVo"/>
        where contact_user_id in (
            select user_id from sys_user where org_id = #{orgId}
            union
            select user_id from sys_user where org_id in (
                select org_id from sys_dept where parent_id = #{orgId}
            )
        )
        order by notification_time desc
    </select>

    <select id="selectNotificationsByWarningIdAndOrgId" resultMap="WeatherWarningNotificationResult">
        <include refid="selectWeatherWarningNotificationVo"/>
        where warning_id = #{warningId}
        and contact_user_id in (
            select user_id from sys_user where org_id = #{orgId}
            union
            select user_id from sys_user where org_id in (
                select org_id from sys_dept where parent_id = #{orgId}
            )
        )
        order by notification_time desc
    </select>

</mapper>
