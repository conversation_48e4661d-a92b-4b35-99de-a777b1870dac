package com.tocc.weather.service;

import java.util.List;
import java.util.Map;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.entity.WeatherWarningArea;
import com.tocc.weather.domain.entity.WeatherWarningNotification;
import com.tocc.weather.domain.dto.WeatherWarningNotifyDTO;
import com.tocc.weather.domain.dto.WeatherWarningNotifyTargetDTO;
import com.tocc.weather.domain.dto.WeatherWarningNotificationDTO;
import com.tocc.weather.domain.vo.WeatherWarningProgressVO;
import com.tocc.weather.domain.vo.WeatherWarningVO;

/**
 * 气象预警通知记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IWeatherWarningNotificationService 
{
    /**
     * 查询气象预警通知记录
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @return 气象预警通知记录
     */
    public WeatherWarningNotification selectWeatherWarningNotificationByIds(String warningId, Long contactUserId);

    /**
     * 查询气象预警通知记录列表
     * 
     * @param warningId 预警ID
     * @return 气象预警通知记录集合
     */
    public List<WeatherWarningNotification> selectWeatherWarningNotificationList(String warningId);

    /**
     * 查询用户的预警通知列表
     * 
     * @param contactUserId 联系人用户ID
     * @return 气象预警通知记录集合
     */
    public List<WeatherWarningNotification> selectNotificationsByUserId(Long contactUserId);

    /**
     * 新增气象预警通知记录
     * 
     * @param weatherWarningNotification 气象预警通知记录
     * @return 结果
     */
    public int insertWeatherWarningNotification(WeatherWarningNotification weatherWarningNotification);

    /**
     * 修改气象预警通知记录
     * 
     * @param weatherWarningNotification 气象预警通知记录
     * @return 结果
     */
    public int updateWeatherWarningNotification(WeatherWarningNotification weatherWarningNotification);

    /**
     * 批量删除气象预警通知记录
     * 
     * @param warningIds 需要删除的预警ID集合
     * @return 结果
     */
    public int deleteWeatherWarningNotificationByWarningIds(String[] warningIds);

    /**
     * 删除预警的所有通知记录
     * 
     * @param warningId 预警ID
     * @return 结果
     */
    public int deleteWeatherWarningNotificationByWarningId(String warningId);

    /**
     * 批量创建通知记录
     *
     * @param warningId 预警ID
     * @param areas 影响区域列表
     * @param notifyTargets 通知对象列表
     * @return 结果
     */
    public int batchCreateNotifications(String warningId, List<WeatherWarningArea> areas, List<WeatherWarningNotifyDTO> notifyTargets);

    /**
     * 批量创建通知记录（新版本）
     *
     * @param warningId 预警ID
     * @param notifyTargets 通知对象列表
     * @return 结果
     */
    public int batchCreateNotifications(String warningId, List<WeatherWarningNotifyTargetDTO> notifyTargets);

    /**
     * 确认预警通知
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @param confirmUserId 确认人ID
     * @param confirmUserName 确认人姓名
     * @return 结果
     */
    public int confirmNotification(String warningId, Long contactUserId, Long confirmUserId, String confirmUserName);

    /**
     * 查询预警通知进展
     * 
     * @param warningId 预警ID
     * @return 通知进展列表
     */
    public List<WeatherWarningProgressVO> selectNotificationProgress(String warningId);

    /**
     * 查询超时的通知记录
     * 
     * @return 超时通知记录列表
     */
    public List<WeatherWarningNotification> selectTimeoutNotifications();

    /**
     * 查询未确认的通知记录
     * 
     * @param warningId 预警ID
     * @return 未确认通知记录列表
     */
    public List<WeatherWarningNotification> selectUnconfirmedNotifications(String warningId);

    /**
     * 更新通知超时状态
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @param alarmId 告警ID
     * @return 结果
     */
    public int updateNotificationTimeout(String warningId, Long contactUserId, String alarmId);

    /**
     * 催办未确认通知
     * 
     * @param warningId 预警ID
     * @return 结果
     */
    public int remindUnconfirmedNotifications(String warningId);

    /**
     * 发送通知
     * 
     * @param notifications 通知记录列表
     */
    public void sendNotifications(List<WeatherWarningNotification> notifications);

    /**
     * 发送单个通知
     *
     * @param notification 通知记录
     * @param warning 预警信息
     */
    public void sendSingleNotification(WeatherWarningNotification notification, WeatherWarning warning);

    /**
     * 发送短信催办未确认的通知
     *
     * @param warningId 预警ID
     * @return 发送短信数量
     */
    public int sendSmsReminder(String warningId);

    /**
     * 查询通知列表（通用查询）
     *
     * @param queryDTO 查询条件
     * @return 通知列表
     */
    public List<WeatherWarningNotification> selectNotificationList(WeatherWarningNotificationDTO queryDTO);

    /**
     * 根据告警ID查询预警详情
     *
     * @param alarmId 告警ID
     * @return 预警详情
     */
    public WeatherWarningVO getWarningDetailByAlarmId(String alarmId);

    /**
     * 根据告警ID确认预警通知
     *
     * @param alarmId 告警ID
     * @param confirmUserId 确认人ID
     * @param confirmUserName 确认人姓名
     * @return 结果
     */
    public int confirmNotificationByAlarmId(String alarmId, Long confirmUserId, String confirmUserName);

    /**
     * 查询单位内所有通知记录
     *
     * @param orgId 单位ID
     * @return 通知记录列表
     */
    public List<WeatherWarningNotification> selectNotificationsByOrgId(String orgId);

    /**
     * 统计预警通知情况
     *
     * @param warningId 预警ID
     * @return 统计结果Map
     */
    public Map<String, Object> selectNotificationStats(String warningId);

    /**
     * 根据预警ID和单位ID查询通知记录
     *
     * @param warningId 预警ID
     * @param orgId 单位ID
     * @return 通知记录列表
     */
    public List<WeatherWarningNotification> selectNotificationsByWarningIdAndOrgId(String warningId, String orgId);
}
