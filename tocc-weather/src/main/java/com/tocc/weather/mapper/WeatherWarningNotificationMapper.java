package com.tocc.weather.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.tocc.weather.domain.entity.WeatherWarningNotification;
import com.tocc.weather.domain.dto.WeatherWarningNotificationDTO;
import com.tocc.weather.domain.vo.WeatherWarningProgressVO;

/**
 * 气象预警通知记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface WeatherWarningNotificationMapper 
{
    /**
     * 查询气象预警通知记录
     *
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @return 气象预警通知记录
     */
    public WeatherWarningNotification selectWeatherWarningNotificationByIds(@Param("warningId") String warningId,
                                                                           @Param("contactUserId") Long contactUserId);

    /**
     * 查询气象预警通知记录列表
     * 
     * @param warningId 预警ID
     * @return 气象预警通知记录集合
     */
    public List<WeatherWarningNotification> selectWeatherWarningNotificationList(String warningId);

    /**
     * 查询用户的预警通知列表
     * 
     * @param contactUserId 联系人用户ID
     * @return 气象预警通知记录集合
     */
    public List<WeatherWarningNotification> selectNotificationsByUserId(Long contactUserId);

    /**
     * 新增气象预警通知记录
     * 
     * @param weatherWarningNotification 气象预警通知记录
     * @return 结果
     */
    public int insertWeatherWarningNotification(WeatherWarningNotification weatherWarningNotification);

    /**
     * 批量新增气象预警通知记录
     * 
     * @param weatherWarningNotificationList 气象预警通知记录列表
     * @return 结果
     */
    public int batchInsertWeatherWarningNotification(List<WeatherWarningNotification> weatherWarningNotificationList);

    /**
     * 修改气象预警通知记录
     * 
     * @param weatherWarningNotification 气象预警通知记录
     * @return 结果
     */
    public int updateWeatherWarningNotification(WeatherWarningNotification weatherWarningNotification);

    /**
     * 删除气象预警通知记录
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @return 结果
     */
    public int deleteWeatherWarningNotificationByIds(String warningId, Long contactUserId);

    /**
     * 删除预警的所有通知记录
     * 
     * @param warningId 预警ID
     * @return 结果
     */
    public int deleteWeatherWarningNotificationByWarningId(String warningId);

    /**
     * 批量删除气象预警通知记录
     * 
     * @param warningIds 需要删除的预警ID集合
     * @return 结果
     */
    public int deleteWeatherWarningNotificationByWarningIds(String[] warningIds);

    /**
     * 确认预警通知
     *
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @param confirmUserId 确认人ID
     * @param confirmUserName 确认人姓名
     * @return 结果
     */
    public int confirmNotification(@Param("warningId") String warningId,
                                 @Param("contactUserId") Long contactUserId,
                                 @Param("confirmUserId") Long confirmUserId,
                                 @Param("confirmUserName") String confirmUserName);

    /**
     * 查询预警通知进展
     * 
     * @param warningId 预警ID
     * @return 通知进展列表
     */
    public List<WeatherWarningProgressVO> selectNotificationProgress(String warningId);

    /**
     * 查询超时的通知记录
     * 
     * @return 超时通知记录列表
     */
    public List<WeatherWarningNotification> selectTimeoutNotifications();

    /**
     * 查询未确认的通知记录
     * 
     * @param warningId 预警ID
     * @return 未确认通知记录列表
     */
    public List<WeatherWarningNotification> selectUnconfirmedNotifications(String warningId);

    /**
     * 更新通知超时状态
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @param alarmId 告警ID
     * @return 结果
     */
    public int updateNotificationTimeout(String warningId, Long contactUserId, String alarmId);

    /**
     * 统计预警通知情况
     *
     * @param warningId 预警ID
     * @return 统计结果Map
     */
    public java.util.Map<String, Object> selectNotificationStats(String warningId);

    /**
     * 查询通知列表（通用查询）
     *
     * @param queryDTO 查询条件
     * @return 通知列表
     */
    public List<WeatherWarningNotification> selectNotificationList(WeatherWarningNotificationDTO queryDTO);

    /**
     * 查询单位内所有通知记录
     *
     * @param orgId 单位ID
     * @return 通知记录列表
     */
    public List<WeatherWarningNotification> selectNotificationsByOrgId(String orgId);

    /**
     * 根据预警ID和单位ID查询通知记录
     *
     * @param warningId 预警ID
     * @param orgId 单位ID
     * @return 通知记录列表
     */
    public List<WeatherWarningNotification> selectNotificationsByWarningIdAndOrgId(@Param("warningId") String warningId,
                                                                                   @Param("orgId") String orgId);
}
