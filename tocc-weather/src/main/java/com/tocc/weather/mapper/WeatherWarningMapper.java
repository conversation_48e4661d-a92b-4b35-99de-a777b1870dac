package com.tocc.weather.mapper;

import java.util.List;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.dto.WeatherWarningDTO;

/**
 * 气象预警信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface WeatherWarningMapper 
{
    /**
     * 查询气象预警信息
     * 
     * @param warningId 气象预警信息主键
     * @return 气象预警信息
     */
    public WeatherWarning selectWeatherWarningByWarningId(String warningId);

    /**
     * 查询气象预警信息列表
     * 
     * @param weatherWarningDTO 气象预警信息查询条件
     * @return 气象预警信息集合
     */
    public List<WeatherWarning> selectWeatherWarningList(WeatherWarningDTO weatherWarningDTO);

    /**
     * 新增气象预警信息
     * 
     * @param weatherWarning 气象预警信息
     * @return 结果
     */
    public int insertWeatherWarning(WeatherWarning weatherWarning);

    /**
     * 修改气象预警信息
     * 
     * @param weatherWarning 气象预警信息
     * @return 结果
     */
    public int updateWeatherWarning(WeatherWarning weatherWarning);

    /**
     * 删除气象预警信息
     * 
     * @param warningId 气象预警信息主键
     * @return 结果
     */
    public int deleteWeatherWarningByWarningId(String warningId);

    /**
     * 批量删除气象预警信息
     * 
     * @param warningIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWeatherWarningByWarningIds(String[] warningIds);

    /**
     * 更新预警状态
     * 
     * @param warningId 预警ID
     * @param status 状态
     * @return 结果
     */
    public int updateWeatherWarningStatus(String warningId, String status);

    /**
     * 查询预警统计信息（包含通知统计）
     * 
     * @param warningId 预警ID
     * @return 预警信息（包含统计数据）
     */
    public WeatherWarning selectWeatherWarningWithStats(String warningId);

    /**
     * 查询有效的预警列表（用于定时任务）
     * 
     * @return 有效预警列表
     */
    public List<WeatherWarning> selectActiveWarnings();

    /**
     * 查询即将过期的预警列表
     *
     * @param minutes 提前多少分钟
     * @return 即将过期的预警列表
     */
    public List<WeatherWarning> selectExpiringWarnings(int minutes);

    /**
     * 查询用户创建的预警列表
     *
     * @param createBy 创建者
     * @return 预警列表
     */
    public List<WeatherWarning> selectWarningsByCreator(String createBy);

    /**
     * 查询单位及上层单位创建的预警列表
     *
     * @param orgId 单位ID
     * @return 预警列表
     */
    public List<WeatherWarning> selectWarningsByOrgId(String orgId);

    /**
     * 批量更新过期预警状态为已失效
     *
     * @return 更新的记录数
     */
    public int updateExpiredWarningsStatus();

    /**
     * 查询过期但状态仍为有效的预警列表（用于日志记录）
     *
     * @return 过期预警列表
     */
    public List<WeatherWarning> selectExpiredActiveWarnings();
}
