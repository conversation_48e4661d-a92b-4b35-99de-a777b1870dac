package com.tocc.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.AgoraEventType;
import com.tocc.config.AgoraConfig;
import com.tocc.domain.AgoraWebhookEvent;
import com.tocc.domain.DispatchMeeting;
import com.tocc.mapper.DispatchMeetingMapper;
import com.tocc.service.IAgoraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * 声网Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Slf4j
@Service
public class AgoraServiceImpl implements IAgoraService {

    @Resource
    private AgoraConfig agoraConfig;

    @Resource
    private DispatchMeetingMapper dispatchMeetingMapper;


    @Override
    public AjaxResult handleWebhook(String rawBody, String signature) {
        try {
            //验证签名
            log.info("收到声网Webhook事件,rowBody:{},signature:{}", rawBody, signature);
            if (!verifySignature(rawBody, signature, agoraConfig.getWebhookSecret())) {
                log.warn("无效的签名: {}", signature);
                return AjaxResult.error("签名验证失败");
            }
            //入参解析
            AgoraWebhookEvent event = JSONUtil.toBean(rawBody, AgoraWebhookEvent.class);
            //处理不同事件类型
            AgoraEventType eventType = AgoraEventType.fromCode(event.getEventType());
            if (eventType == null) {
                log.warn("未知的事件类型 | eventType: {}", event.getEventType());
                return AjaxResult.error("不支持的事件类型");
            }
            switch (eventType) {
                //频道销毁事件
                case CHANNEL_DESTROY:
                    return handleChannelDestroyEvent(event);
                default:
                    log.warn("未知的事件类型: {}", event.getEventType());
                    return AjaxResult.error("不支持的事件类型");
            }
        } catch (Exception e) {
            log.error("处理Webhook时发生错误", e);
            return AjaxResult.error("服务器处理错误: " + e.getMessage());
        }
    }

    /**
     * 验证签名
     *
     * @param rawBody
     * @param signature
     * @param secret
     * @return
     */
    private boolean verifySignature(String rawBody, String signature, String secret) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes("utf-8"), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(rawBody.getBytes("utf-8"));
            String computedSignature = bytesToHex(rawHmac);
            return computedSignature.equals(signature);
        } catch (Exception e) {
            log.error("计算签名时出错", e);
            return false;
        }
    }

    /**
     * 处理频道销毁事件
     *
     * @param event
     * @return
     */
    private AjaxResult handleChannelDestroyEvent(AgoraWebhookEvent event) {
        //处理频道相关事件
        log.info("处理频道事件: {}", event.getPayload());
        JSONObject payload = event.getPayload();
        if (ObjectUtil.isNotNull(payload)) {
            String channelName = payload.getStr("channelName");
            DispatchMeeting meetingInfoByChannelName = dispatchMeetingMapper.getMeetingInfoByChannelName(channelName);
            if (ObjectUtil.isNotNull(meetingInfoByChannelName)) {
                meetingInfoByChannelName.setStatus("ended");
                dispatchMeetingMapper.updateDispatchMeeting(meetingInfoByChannelName);
            }
        }
        return AjaxResult.success("频道事件处理成功", event.getPayload());
    }

    /**
     * 将加密后的字节数组转换成字符串
     *
     * @param bytes
     * @return
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() < 2) {
                sb.append(0);
            }
            sb.append(hex);
        }
        return sb.toString();
    }

}
