package com.tocc.domain;

import cn.hutool.json.JSONObject;
import lombok.Data;

@Data
public class AgoraWebhookEvent {

    /**
     * 通知ID
     */
    private String noticeId;

    /**
     * 业务ID (1=实时通信业务)
     */
    private Integer productId;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 事件通知的Unix时间戳(ms)
     */
    private Long notifyMs;

    /**
     * 会话ID
     */
    private String sid;

    /**
     * 事件具体内容
     */
    private JSONObject payload;

}