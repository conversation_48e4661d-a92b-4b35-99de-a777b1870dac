package com.tocc.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AddressBookGroupVO {

    @ApiModelProperty(value = "分组")
    private String group;

    @ApiModelProperty(value = "该分组下的联系人列表")
    private List<AddressBookVO> contacts;

    @ApiModelProperty(value = "该分组下的联系人数量")
    private Integer count;

    public AddressBookGroupVO() {
    }

    public AddressBookGroupVO(String groupName, List<AddressBookVO> contacts) {
        this.group = groupName;
        this.contacts = contacts;
        this.count = contacts != null ? contacts.size() : 0;
    }
} 