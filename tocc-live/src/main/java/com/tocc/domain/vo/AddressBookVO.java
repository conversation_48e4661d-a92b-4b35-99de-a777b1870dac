package com.tocc.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AddressBookVO {

    @ApiModelProperty(value = "联系人所在分组")
    private String groupName;

    @ApiModelProperty(value = "联系人名称")
    private String userName;

    @ApiModelProperty(value = "联系人所在单位")
    private String deptName;

    @ApiModelProperty(value = "联系人岗位/角色")
    private String post;

    @ApiModelProperty(value = "联系方式")
    private String contactNumber;
}
