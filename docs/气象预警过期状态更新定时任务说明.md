# 气象预警过期状态更新定时任务说明

## 任务概述

**任务名称：** 气象预警过期状态更新  
**任务编号：** 101（建议）  
**任务分组：** 默认  
**创建时间：** 2024-12-20  

## 功能描述

该定时任务用于自动检查并更新已过期的气象预警状态，将状态从"有效"(0)更新为"已失效"(1)，确保预警信息的时效性和准确性。

## 任务配置

### 基本信息
- **调用目标方法：** `weatherWarningExpiredUpdateTask.execute()`
- **cron表达式：** `0 */30 * * * ?` （每30分钟执行一次）
- **任务状态：** 正常
- **是否并发：** 禁止
- **执行策略：** 立即执行

### 执行频率说明
- **推荐频率：** 每30分钟执行一次
- **高频率：** 每5分钟执行一次（高实时性要求）
- **低频率：** 每小时执行一次（低实时性要求）

## 业务逻辑

### 处理范围
- **目标数据：** 状态为"有效"(status=0)且已过期的气象预警
- **时间范围：** 最近30天内的预警数据
- **过期判断：** `expire_time < now()` 且 `expire_time IS NOT NULL`

### 更新操作
```sql
UPDATE weather_warning 
SET status = '1',
    update_time = NOW(),
    update_by = 'system'
WHERE status = '0'
  AND expire_time IS NOT NULL
  AND expire_time < NOW()
  AND expire_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
```

### 执行流程
1. 查询符合条件的过期预警
2. 记录详细日志信息
3. 批量更新预警状态
4. 输出执行结果统计

## 日志输出

### 正常执行日志
```
[INFO] 开始执行气象预警过期状态更新任务，参数：null
[INFO] 发现 3 条过期的有效预警，准备更新状态
[INFO] 预警即将失效：warningId=xxx, warningType=2, warningLevel=7, expireTime=2024-12-20 14:30:00, createBy=admin
[INFO] 成功更新 3 条过期预警状态为已失效
[INFO] 气象预警过期状态更新任务执行完成
```

### 无数据日志
```
[INFO] 开始执行气象预警过期状态更新任务，参数：null
[INFO] 没有发现过期的有效预警
[INFO] 气象预警过期状态更新任务执行完成
```

### 异常日志
```
[ERROR] 气象预警过期状态更新任务执行失败
java.lang.RuntimeException: 更新过期预警状态失败：...
```

## 性能优化

### 数据库索引
建议创建以下索引以提高查询性能：
```sql
-- 复合索引：状态 + 过期时间
CREATE INDEX idx_weather_warning_status_expire 
ON weather_warning(status, expire_time);
```

### 数据范围限制
- 只处理最近30天的数据，避免扫描过多历史数据
- 使用批量更新减少数据库交互次数

## 监控指标

### 关键指标
- **执行频率：** 每30分钟
- **平均执行时间：** < 5秒
- **平均更新记录数：** 0-10条
- **错误率：** < 1%

### 告警阈值
- **执行时间超过30秒**
- **连续3次执行失败**
- **单次更新记录数超过100条**

## 手动操作

### 立即执行
在定时任务管理界面点击"立即执行"按钮，或调用API：
```bash
POST /weather/warning/manual-update-expired
```

### 暂停任务
在任务状态异常时，可以暂停任务：
1. 进入定时任务管理
2. 找到对应任务
3. 点击"暂停"按钮

### 修改执行频率
根据业务需要调整cron表达式：
- **每分钟：** `0 * * * * ?`
- **每5分钟：** `0 */5 * * * ?`
- **每30分钟：** `0 */30 * * * ?`
- **每小时：** `0 0 * * * ?`
- **每天：** `0 0 0 * * ?`

## 故障排查

### 常见问题

#### 1. 任务执行失败
**现象：** 日志显示执行失败  
**排查：** 
- 检查数据库连接
- 查看详细错误日志
- 确认weather模块服务正常

#### 2. 更新记录数为0
**现象：** 任务正常执行但无更新记录  
**排查：**
- 确认是否有过期的有效预警
- 检查expire_time字段是否正确设置
- 验证时间判断逻辑

#### 3. 执行时间过长
**现象：** 任务执行超过预期时间  
**排查：**
- 检查数据库索引
- 查看数据量是否异常
- 优化查询条件

### 应急处理
1. **立即暂停任务** - 防止影响系统性能
2. **手动更新** - 使用手动接口临时处理
3. **问题修复** - 解决根本问题后重新启动

## 相关文件

### 核心文件
- **定时任务类：** `tocc-quartz/src/main/java/com/tocc/quartz/task/WeatherWarningExpiredUpdateTask.java`
- **Service接口：** `tocc-weather/src/main/java/com/tocc/weather/service/IWeatherWarningService.java`
- **Service实现：** `tocc-weather/src/main/java/com/tocc/weather/service/impl/WeatherWarningServiceImpl.java`
- **Mapper接口：** `tocc-weather/src/main/java/com/tocc/weather/mapper/WeatherWarningMapper.java`
- **SQL配置：** `tocc-weather/src/main/resources/mapper/weather/WeatherWarningMapper.xml`

### 手动接口
- **Controller：** `tocc-admin/src/main/java/com/tocc/web/weatherController/WeatherWarningController.java`
- **接口路径：** `POST /weather/warning/manual-update-expired`

## 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2024-12-20 | 初始版本，实现基础过期状态更新功能 | tocc |

## 注意事项

1. **时区问题：** 确保服务器时区与业务时区一致
2. **数据备份：** 建议在大批量更新前进行数据备份
3. **业务影响：** 状态更新可能影响前端显示，需要前端配合刷新
4. **权限控制：** 只有系统管理员可以配置和管理定时任务
5. **日志清理：** 定期清理过期的执行日志，避免日志文件过大
