# 应急事件管理接口文档

## 概述

本文档描述了应急事件管理系统的核心接口，包括新增事件、状态修改和详情查询功能。系统支持道路交通事故和水路交通事故两种事件类型，每种类型都有对应的专项信息字段。

## 接口列表

### 1. 新增应急事件

**接口地址：** `POST /emergency/event/add`

**接口描述：** 创建新的应急事件，支持关联多个项目运营企业

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| eventTitle | String | 是 | 事件标题，最大200字符 |
| eventType | String | 是 | 事件类型（1=道路交通事故，2=水路交通事故） |
| accidentType | String | 是 | 事故类型（字典值） |
| occurTime | Long | 是 | 发生时间（时间戳，秒） |
| administrativeArea | String | 否 | 行政区名称 |
| administrativeAreaId | String | 否 | 行政区ID |
| reporterId | String | 否 | 填报人ID |
| submitterId | String | 否 | 上报人ID |
| roadManagerUnitId | String | 否 | 路段管辖单位ID |
| roadManagerLeaderId | String | 否 | 路段管辖单位负责人ID |
| detailedAddress | String | 否 | 事故详细地址 |
| longitude | BigDecimal | 否 | 经度 |
| latitude | BigDecimal | 否 | 纬度 |
| impactScope | String | 否 | 影响范围 |
| eventDescription | String | 否 | 事件描述 |
| eventCause | String | 否 | 事件原因 |
| emergencyMeasures | String | 否 | 已采取的应急处置措施 |
| emergencyForces | String | 否 | 投入的应急力量 |
| supportNeeded | String | 否 | 需上级应急指挥机构支持事项 |
| remark | String | 否 | 备注 |
| enterprisePersonnelIds | List<String> | 否 | 关联的项目运营企业ID列表 |

**道路交通事故专项字段：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roadSectionCode | String | 否 | 路段编号 |
| startStakeNumber | String | 否 | 开始桩号 |
| endStakeNumber | String | 否 | 结束桩号 |
| direction | String | 否 | 方向（字典值） |
| trafficAffected | String | 否 | 是否影响通行（Y是 N否） |
| vehicleType | String | 否 | 事故车型 |
| estimatedRecoveryTime | Long | 否 | 预计恢复时间（时间戳，秒） |
| roadCasualtySituation | String | 否 | 人员伤亡情况 |
| impactTrend | String | 否 | 影响范围及事态发展趋势 |

**水路交通事故专项字段：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| waterwayName | String | 否 | 航道名称 |
| shipName | String | 否 | 船舶名称 |
| shipType | String | 否 | 船舶类型（字典值） |
| shipTonnage | BigDecimal | 否 | 船舶吨位 |
| waterwayCasualtySituation | String | 否 | 人员伤亡情况 |
| cargoInfo | String | 否 | 货物信息 |
| environmentalImpact | String | 否 | 环境影响 |

**请求示例（道路交通事故）：**

```json
{
  "eventTitle": "泉南高速柳州段山体塌方事故",
  "eventType": "1",
  "accidentType": "001",
  "occurTime": 1703145600,
  "administrativeArea": "柳州市",
  "administrativeAreaId": "450200",
  "detailedAddress": "泉南高速K1234+500处",
  "longitude": 109.123456,
  "latitude": 24.654321,
  "impactScope": "双向交通中断",
  "eventDescription": "因连续降雨导致山体塌方，阻断高速公路",
  "eventCause": "连续强降雨引发山体滑坡",
  "emergencyMeasures": "已设置警示标志，疏导车辆绕行",
  "emergencyForces": "交警2人，养护人员5人",
  "supportNeeded": "需要大型清障设备",
  "enterprisePersonnelIds": ["enterprise001", "enterprise002"],
  "roadSectionCode": "G72-001",
  "startStakeNumber": "K1234+000",
  "endStakeNumber": "K1234+800",
  "direction": "1",
  "trafficAffected": "Y",
  "vehicleType": "货车",
  "estimatedRecoveryTime": 1703232000,
  "roadCasualtySituation": "无人员伤亡",
  "impactTrend": "影响范围可能扩大，需密切关注"
}
```

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": 1
}
```

### 2. 修改应急事件状态

**接口地址：** `PUT /emergency/event/status`

**接口描述：** 修改应急事件的状态

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| eventId | String | 是 | 事件ID |
| status | String | 是 | 新状态（0=已上报，1=已确认，2=已完结，3=删除） |

**请求示例：**

```json
{
  "eventId": "event123456",
  "status": "1"
}
```

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": 1
}
```

### 3. 获取应急事件详情

**接口地址：** `GET /emergency/event/{eventId}`

**接口描述：** 根据事件ID获取应急事件的详细信息，包括专项信息和关联企业

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| eventId | String | 是 | 事件ID |

**响应参数：**

基础字段与新增接口相同，额外包含：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| eventId | String | 事件ID |
| status | String | 状态 |
| statusName | String | 状态名称 |
| createTime | Long | 创建时间 |
| updateTime | Long | 更新时间 |
| enterpriseList | List<Object> | 关联的企业列表 |

**企业信息字段：**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| enterprisePersonnelId | String | 企业人员ID |
| enterpriseName | String | 企业名称 |
| principal | String | 负责人姓名 |
| contactWay | String | 联系电话 |

**响应示例（道路交通事故）：**

```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "eventId": "event123456",
    "eventTitle": "泉南高速柳州段山体塌方事故",
    "eventType": "1",
    "eventTypeName": "道路交通事故",
    "accidentType": "001",
    "accidentTypeName": "山体塌方",
    "occurTime": 1703145600,
    "status": "1",
    "statusName": "已确认",
    "createTime": 1703145600,
    "updateTime": 1703232000,
    "detailedAddress": "泉南高速K1234+500处",
    "longitude": 109.123456,
    "latitude": 24.654321,
    "roadSectionCode": "G72-001",
    "startStakeNumber": "K1234+000",
    "endStakeNumber": "K1234+800",
    "direction": "1",
    "trafficAffected": "Y",
    "vehicleType": "货车",
    "estimatedRecoveryTime": 1703232000,
    "roadCasualtySituation": "无人员伤亡",
    "enterpriseList": [
      {
        "enterprisePersonnelId": "enterprise001",
        "enterpriseName": "广西高速公路管理有限公司柳州分公司",
        "principal": "张三",
        "contactWay": "13800138001"
      },
      {
        "enterprisePersonnelId": "enterprise002",
        "enterpriseName": "柳州市交通建设投资有限公司",
        "principal": "李四",
        "contactWay": "13800138002"
      }
    ]
  }
}
```

## Vue3 前端代码示例

### 1. API 接口封装

```javascript
// api/emergency.js
import request from '@/utils/request'

// 新增应急事件
export function addEmergencyEvent(data) {
  return request({
    url: '/emergency/event/add',
    method: 'post',
    data: data
  })
}

// 修改事件状态
export function updateEventStatus(data) {
  return request({
    url: '/emergency/event/status',
    method: 'put',
    data: data
  })
}

// 获取事件详情
export function getEmergencyEventDetail(eventId) {
  return request({
    url: `/emergency/event/${eventId}`,
    method: 'get'
  })
}

// 获取企业列表（用于下拉选择）
export function getEnterpriseList() {
  return request({
    url: '/system/enterprise/list',
    method: 'get'
  })
}
```

### 2. 新增事件表单组件

```vue
<!-- components/EmergencyEventForm.vue -->
<template>
  <div class="emergency-event-form">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <!-- 基础信息 -->
      <el-card class="box-card" header="基础信息">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事件标题" prop="eventTitle">
              <el-input v-model="form.eventTitle" placeholder="请输入事件标题" maxlength="200" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件类型" prop="eventType">
              <el-select v-model="form.eventType" placeholder="请选择事件类型" @change="handleEventTypeChange">
                <el-option label="道路交通事故" value="1" />
                <el-option label="水路交通事故" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事故类型" prop="accidentType">
              <el-select v-model="form.accidentType" placeholder="请选择事故类型">
                <el-option label="山体塌方" value="001" />
                <el-option label="车辆碰撞" value="002" />
                <!-- 更多选项... -->
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生时间" prop="occurTime">
              <el-date-picker
                v-model="occurTimeDate"
                type="datetime"
                placeholder="选择发生时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="x"
                @change="handleOccurTimeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="关联企业">
              <el-select
                v-model="form.enterprisePersonnelIds"
                multiple
                placeholder="请选择关联的项目运营企业"
                style="width: 100%"
              >
                <el-option
                  v-for="enterprise in enterpriseOptions"
                  :key="enterprise.enterprisePersonnelId"
                  :label="enterprise.enterpriseName"
                  :value="enterprise.enterprisePersonnelId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="事件描述">
          <el-input
            v-model="form.eventDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入事件描述"
          />
        </el-form-item>
      </el-card>

      <!-- 道路交通事故专项信息 -->
      <el-card v-if="form.eventType === '1'" class="box-card" header="道路交通事故专项信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="路段编号">
              <el-input v-model="form.roadSectionCode" placeholder="请输入路段编号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开始桩号">
              <el-input v-model="form.startStakeNumber" placeholder="请输入开始桩号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束桩号">
              <el-input v-model="form.endStakeNumber" placeholder="请输入结束桩号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否影响通行">
              <el-radio-group v-model="form.trafficAffected">
                <el-radio label="Y">是</el-radio>
                <el-radio label="N">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计恢复时间">
              <el-date-picker
                v-model="estimatedRecoveryTimeDate"
                type="datetime"
                placeholder="选择预计恢复时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="x"
                @change="handleEstimatedRecoveryTimeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="人员伤亡情况">
          <el-input
            v-model="form.roadCasualtySituation"
            type="textarea"
            :rows="2"
            placeholder="请输入人员伤亡情况"
          />
        </el-form-item>
      </el-card>

      <!-- 水路交通事故专项信息 -->
      <el-card v-if="form.eventType === '2'" class="box-card" header="水路交通事故专项信息">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="航道名称">
              <el-input v-model="form.waterwayName" placeholder="请输入航道名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="船舶名称">
              <el-input v-model="form.shipName" placeholder="请输入船舶名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="船舶类型">
              <el-select v-model="form.shipType" placeholder="请选择船舶类型">
                <el-option label="货船" value="1" />
                <el-option label="客船" value="2" />
                <el-option label="油轮" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="船舶吨位">
              <el-input-number
                v-model="form.shipTonnage"
                :min="0"
                :precision="2"
                placeholder="请输入船舶吨位"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="货物信息">
          <el-input
            v-model="form.cargoInfo"
            type="textarea"
            :rows="2"
            placeholder="请输入货物信息"
          />
        </el-form-item>

        <el-form-item label="环境影响">
          <el-input
            v-model="form.environmentalImpact"
            type="textarea"
            :rows="2"
            placeholder="请输入环境影响"
          />
        </el-form-item>
      </el-card>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { addEmergencyEvent, getEnterpriseList } from '@/api/emergency'

// 响应式数据
const formRef = ref()
const loading = ref(false)
const occurTimeDate = ref('')
const estimatedRecoveryTimeDate = ref('')
const enterpriseOptions = ref([])

// 表单数据
const form = reactive({
  eventTitle: '',
  eventType: '',
  accidentType: '',
  occurTime: null,
  administrativeArea: '',
  administrativeAreaId: '',
  detailedAddress: '',
  longitude: null,
  latitude: null,
  impactScope: '',
  eventDescription: '',
  eventCause: '',
  emergencyMeasures: '',
  emergencyForces: '',
  supportNeeded: '',
  remark: '',
  enterprisePersonnelIds: [],
  // 道路交通事故字段
  roadSectionCode: '',
  startStakeNumber: '',
  endStakeNumber: '',
  direction: '',
  trafficAffected: '',
  vehicleType: '',
  estimatedRecoveryTime: null,
  roadCasualtySituation: '',
  impactTrend: '',
  // 水路交通事故字段
  waterwayName: '',
  shipName: '',
  shipType: '',
  shipTonnage: null,
  waterwayCasualtySituation: '',
  cargoInfo: '',
  environmentalImpact: ''
})

// 表单验证规则
const rules = {
  eventTitle: [
    { required: true, message: '请输入事件标题', trigger: 'blur' },
    { max: 200, message: '事件标题长度不能超过200个字符', trigger: 'blur' }
  ],
  eventType: [
    { required: true, message: '请选择事件类型', trigger: 'change' }
  ],
  accidentType: [
    { required: true, message: '请选择事故类型', trigger: 'change' }
  ],
  occurTime: [
    { required: true, message: '请选择发生时间', trigger: 'change' }
  ]
}

// 方法
const handleEventTypeChange = () => {
  // 切换事件类型时清空专项字段
  if (form.eventType === '1') {
    // 清空水路交通事故字段
    form.waterwayName = ''
    form.shipName = ''
    form.shipType = ''
    form.shipTonnage = null
    form.waterwayCasualtySituation = ''
    form.cargoInfo = ''
    form.environmentalImpact = ''
  } else if (form.eventType === '2') {
    // 清空道路交通事故字段
    form.roadSectionCode = ''
    form.startStakeNumber = ''
    form.endStakeNumber = ''
    form.direction = ''
    form.trafficAffected = ''
    form.vehicleType = ''
    form.estimatedRecoveryTime = null
    form.roadCasualtySituation = ''
    form.impactTrend = ''
  }
}

const handleOccurTimeChange = (value) => {
  form.occurTime = value ? Math.floor(value / 1000) : null
}

const handleEstimatedRecoveryTimeChange = (value) => {
  form.estimatedRecoveryTime = value ? Math.floor(value / 1000) : null
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    const response = await addEmergencyEvent(form)
    if (response.code === 200) {
      ElMessage.success('应急事件创建成功')
      resetForm()
    } else {
      ElMessage.error(response.msg || '创建失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请检查表单数据')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  occurTimeDate.value = ''
  estimatedRecoveryTimeDate.value = ''
  Object.keys(form).forEach(key => {
    if (typeof form[key] === 'string') {
      form[key] = ''
    } else if (typeof form[key] === 'number') {
      form[key] = null
    } else if (Array.isArray(form[key])) {
      form[key] = []
    }
  })
}

const loadEnterpriseOptions = async () => {
  try {
    const response = await getEnterpriseList()
    if (response.code === 200) {
      enterpriseOptions.value = response.data || []
    }
  } catch (error) {
    console.error('加载企业列表失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadEnterpriseOptions()
})
</script>

<style scoped>
.emergency-event-form {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}
</style>
```

### 3. 事件详情展示组件

```vue
<!-- components/EmergencyEventDetail.vue -->
<template>
  <div class="emergency-event-detail">
    <el-card class="box-card" header="事件详情" v-loading="loading">
      <!-- 基础信息 -->
      <el-descriptions title="基础信息" :column="2" border>
        <el-descriptions-item label="事件标题">{{ eventDetail.eventTitle }}</el-descriptions-item>
        <el-descriptions-item label="事件类型">{{ eventDetail.eventTypeName }}</el-descriptions-item>
        <el-descriptions-item label="事故类型">{{ eventDetail.accidentTypeName }}</el-descriptions-item>
        <el-descriptions-item label="发生时间">{{ formatTime(eventDetail.occurTime) }}</el-descriptions-item>
        <el-descriptions-item label="行政区域">{{ eventDetail.administrativeArea }}</el-descriptions-item>
        <el-descriptions-item label="详细地址">{{ eventDetail.detailedAddress }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(eventDetail.status)">{{ eventDetail.statusName }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatTime(eventDetail.createTime) }}</el-descriptions-item>
      </el-descriptions>

      <!-- 事件描述 -->
      <el-divider content-position="left">事件描述</el-divider>
      <p>{{ eventDetail.eventDescription || '暂无描述' }}</p>

      <!-- 关联企业 -->
      <el-divider content-position="left">关联企业</el-divider>
      <el-table :data="eventDetail.enterpriseList" style="width: 100%" v-if="eventDetail.enterpriseList?.length">
        <el-table-column prop="enterpriseName" label="企业名称" />
        <el-table-column prop="principal" label="负责人" />
        <el-table-column prop="contactWay" label="联系方式" />
      </el-table>
      <el-empty v-else description="暂无关联企业" />

      <!-- 道路交通事故专项信息 -->
      <template v-if="eventDetail.eventType === '1'">
        <el-divider content-position="left">道路交通事故专项信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="路段编号">{{ eventDetail.roadSectionCode }}</el-descriptions-item>
          <el-descriptions-item label="开始桩号">{{ eventDetail.startStakeNumber }}</el-descriptions-item>
          <el-descriptions-item label="结束桩号">{{ eventDetail.endStakeNumber }}</el-descriptions-item>
          <el-descriptions-item label="是否影响通行">
            <el-tag :type="eventDetail.trafficAffected === 'Y' ? 'danger' : 'success'">
              {{ eventDetail.trafficAffected === 'Y' ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="事故车型">{{ eventDetail.vehicleType }}</el-descriptions-item>
          <el-descriptions-item label="预计恢复时间">{{ formatTime(eventDetail.estimatedRecoveryTime) }}</el-descriptions-item>
          <el-descriptions-item label="人员伤亡情况" :span="2">{{ eventDetail.roadCasualtySituation }}</el-descriptions-item>
        </el-descriptions>
      </template>

      <!-- 水路交通事故专项信息 -->
      <template v-if="eventDetail.eventType === '2'">
        <el-divider content-position="left">水路交通事故专项信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="航道名称">{{ eventDetail.waterwayName }}</el-descriptions-item>
          <el-descriptions-item label="船舶名称">{{ eventDetail.shipName }}</el-descriptions-item>
          <el-descriptions-item label="船舶类型">{{ eventDetail.shipTypeName }}</el-descriptions-item>
          <el-descriptions-item label="船舶吨位">{{ eventDetail.shipTonnage }}吨</el-descriptions-item>
          <el-descriptions-item label="货物信息" :span="2">{{ eventDetail.cargoInfo }}</el-descriptions-item>
          <el-descriptions-item label="人员伤亡情况" :span="2">{{ eventDetail.waterwayCasualtySituation }}</el-descriptions-item>
          <el-descriptions-item label="环境影响" :span="2">{{ eventDetail.environmentalImpact }}</el-descriptions-item>
        </el-descriptions>
      </template>

      <!-- 操作按钮 -->
      <div class="detail-actions">
        <el-button
          v-if="eventDetail.status === '0'"
          type="primary"
          @click="updateStatus('1')"
          :loading="statusLoading"
        >
          确认事件
        </el-button>
        <el-button
          v-if="eventDetail.status === '1'"
          type="success"
          @click="updateStatus('2')"
          :loading="statusLoading"
        >
          完结事件
        </el-button>
        <el-button @click="refreshDetail">刷新</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getEmergencyEventDetail, updateEventStatus } from '@/api/emergency'

// Props
const props = defineProps({
  eventId: {
    type: String,
    required: true
  }
})

// 响应式数据
const loading = ref(false)
const statusLoading = ref(false)
const eventDetail = ref({})

// 方法
const loadEventDetail = async () => {
  try {
    loading.value = true
    const response = await getEmergencyEventDetail(props.eventId)
    if (response.code === 200) {
      eventDetail.value = response.data || {}
    } else {
      ElMessage.error(response.msg || '获取事件详情失败')
    }
  } catch (error) {
    console.error('获取事件详情失败:', error)
    ElMessage.error('获取事件详情失败')
  } finally {
    loading.value = false
  }
}

const updateStatus = async (newStatus) => {
  const statusMap = {
    '1': '确认',
    '2': '完结'
  }

  try {
    await ElMessageBox.confirm(
      `确定要${statusMap[newStatus]}此事件吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    statusLoading.value = true
    const response = await updateEventStatus({
      eventId: props.eventId,
      status: newStatus
    })

    if (response.code === 200) {
      ElMessage.success(`事件${statusMap[newStatus]}成功`)
      await loadEventDetail() // 刷新详情
    } else {
      ElMessage.error(response.msg || `${statusMap[newStatus]}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新状态失败:', error)
      ElMessage.error('操作失败')
    }
  } finally {
    statusLoading.value = false
  }
}

const refreshDetail = () => {
  loadEventDetail()
}

const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp * 1000)
  return date.toLocaleString('zh-CN')
}

const getStatusType = (status) => {
  const typeMap = {
    '0': 'warning',  // 已上报
    '1': 'primary',  // 已确认
    '2': 'success',  // 已完结
    '3': 'danger'    // 删除
  }
  return typeMap[status] || 'info'
}

// 生命周期
onMounted(() => {
  loadEventDetail()
})

// 暴露方法给父组件
defineExpose({
  refreshDetail
})
</script>

<style scoped>
.emergency-event-detail {
  padding: 20px;
}

.detail-actions {
  text-align: center;
  margin-top: 30px;
}

.detail-actions .el-button {
  margin: 0 10px;
}

.el-divider {
  margin: 30px 0 20px 0;
}
</style>
```

### 4. 使用示例

```vue
<!-- pages/EmergencyEventManage.vue -->
<template>
  <div class="emergency-event-manage">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="新增事件" name="add">
        <EmergencyEventForm />
      </el-tab-pane>
      <el-tab-pane label="事件详情" name="detail">
        <div style="margin-bottom: 20px;">
          <el-input
            v-model="eventId"
            placeholder="请输入事件ID"
            style="width: 300px; margin-right: 10px;"
          />
          <el-button type="primary" @click="loadDetail">查看详情</el-button>
        </div>
        <EmergencyEventDetail v-if="eventId" :event-id="eventId" ref="detailRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import EmergencyEventForm from '@/components/EmergencyEventForm.vue'
import EmergencyEventDetail from '@/components/EmergencyEventDetail.vue'

const activeTab = ref('add')
const eventId = ref('')
const detailRef = ref()

const loadDetail = () => {
  if (eventId.value) {
    detailRef.value?.refreshDetail()
  }
}
</script>

<style scoped>
.emergency-event-manage {
  padding: 20px;
}
</style>
```

## 注意事项

1. **时间格式转换**：前端传递时间戳时需要转换为秒（除以1000）
2. **企业多选**：支持选择多个关联企业，传递企业ID数组
3. **事件类型切换**：切换事件类型时会清空对应的专项字段
4. **状态管理**：只有特定状态的事件才能进行状态变更操作
5. **权限控制**：实际使用时需要根据用户权限控制操作按钮的显示

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |